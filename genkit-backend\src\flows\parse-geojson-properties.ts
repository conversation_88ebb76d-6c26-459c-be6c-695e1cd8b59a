import * as z from 'zod';
// import { defineFlow, runFlow } from '@genkit-ai/flow'; // defineFlow not used directly here anymore
// import { SecretManagerServiceClient } from '@google-cloud/secret-manager'; // No longer needed for direct API key access here
// import { generate } from '@genkit-ai/ai'; // No longer needed for direct generate call here

// Import BAML client and generated types
import { b } from '../../baml_client'; // Adjust path if baml_client is elsewhere relative to this file
import { GeoJsonBamlOutput, GeoJsonParseErrorType } from '../../baml_client/types'; // Ensure this path and type names are correct after baml generate

// Input schema for the flow AND the internal LLM logic function
export const GeoJsonPropertiesInputSchema = z.object({
  nameProperty: z.string().optional().nullable(),
  descriptionProperty: z.string().optional().nullable(),
  // preParsedTipAmount is no longer used
});

// Output schema for the flow AND the internal LLM logic function
export const GeoJsonParseOutputSchema = z.object({
  orderId: z.string().optional().nullable().describe('The unique identifier for the order, if found.'),
  tipAmount: z.number().nullable().describe('The numeric tip amount, or null if not explicitly mentioned or ambiguous.'),
  explicitZeroTipDetected: z.boolean().optional().describe('True if the input text explicitly confirmed a zero-dollar tip.'),
  explicitDndInstructionDetected: z.boolean().optional().describe('True if the input text contained an explicit Do Not Deliver instruction.'),
  note: z.string().optional().nullable().describe('Any additional relevant notes extracted from the text.'),
  // Adding per-item error fields for batch processing
  error: z.string().optional().nullable().describe("If parsing failed for this specific item, provide a description."),
  errorType: z.enum(["parsing_failed", "ambiguous_input", "no_info", "other"]).optional().nullable().describe("Categorization of the specific item error."),
  // originalInputForError: z.string().optional().nullable(), // Keep track of input if needed for errors
});

// Constants for OpenRouter API - NO LONGER NEEDED HERE
// const OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions";
// const OPENROUTER_DEFAULT_MODEL = "deepseek/deepseek-chat-v3-0324"; 

// Define GeoJsonFeature types locally if not imported (adjust as needed)
interface GeoJsonFeatureProperties {
  name?: string | null;
  description?: string | null;
  [key: string]: any;
}
interface GeoJsonFeature {
  type: 'Feature';
  geometry: {
    type: string;
    coordinates: [number, number];
  };
  properties: GeoJsonFeatureProperties;
}

// Secret Manager related constants and functions - NO LONGER NEEDED HERE
// const SECRET_NAME = 'openrouter-api-key';
// const SECRET_VERSION = 'latest';
// const PROJECT_ID = 'autogratuity-me'; 
// const secretResourceName = `projects/${PROJECT_ID}/secrets/${SECRET_NAME}/versions/${SECRET_VERSION}`;
// const secretManagerClient = new SecretManagerServiceClient();
// async function getOpenRouterApiKey(): Promise<string> { ... } // Removed

/**
 * Core logic to extract data from GeoJSON properties using LLM.
 * This function can be called directly by other flows/modules.
 */
export interface LLMCallParams {
  // This interface might still be relevant if BAML allows passing some params through,
  // but BAML client options are usually set in the .baml file client definition.
  // For now, let's assume BAML handles these via its client config.
  // temperature?: number;
  // max_tokens?: number;
  // ... other params ...
}

// --- BATCH PROMPT TEMPLATE - MOVED TO GEOJSON_EXTRACTOR.BAML --- 
// const GEOJSON_PARSE_BATCH_PROMPT_TEMPLATE = `...`; // Removed

// --- NEW BATCH LLM FUNCTION USING BAML --- 
export async function _extractGeoJsonDataBatchLLM(
  chunk: GeoJsonFeature[],
  // params?: LLMCallParams // BAML client params usually set in .baml file
): Promise<z.infer<typeof GeoJsonParseOutputSchema>[]> { 
  const logPrefix = `[_extractGeoJsonDataBatchLLM_BAML] -`;
  if (!chunk || chunk.length === 0) {
    console.log(`${logPrefix} Received empty chunk. Returning empty array.`);
    return [];
  }
  const expectedResultsCount = chunk.length;
  console.log(`${logPrefix} Processing batch of ${expectedResultsCount} features using BAML.`);

  const results: z.infer<typeof GeoJsonParseOutputSchema>[] = [];

  for (const feature of chunk) {
    const nameProp = feature.properties?.name ?? "";
    const descProp = feature.properties?.description ?? "";
    const featurePropertiesText = `${nameProp} ${descProp}`.trim();

    if (!featurePropertiesText) {
      results.push({
        orderId: null,
        tipAmount: null,
        explicitZeroTipDetected: false,
        explicitDndInstructionDetected: false,
        note: null,
        error: "Empty input text from feature properties",
        errorType: "no_info",
      });
      continue;
    }

    try {
      console.log(`${logPrefix} Calling BAML function ExtractGeoJsonProperties for feature text: "${featurePropertiesText.substring(0,100)}..."`);
      // Call the BAML generated function
      const bamlOutput: GeoJsonBamlOutput = await b.ExtractGeoJsonProperties(featurePropertiesText);
      
      console.log(`${logPrefix} Received BAML output:`, JSON.stringify(bamlOutput));

      // Map BAML output to our Zod schema. They should be very similar.
      // BAML enum needs to be mapped to string enum for Zod schema.
      let errorTypeString: "parsing_failed" | "ambiguous_input" | "no_info" | "other" | null | undefined = null;
      if (bamlOutput.errorType) {
        switch (bamlOutput.errorType) {
          case GeoJsonParseErrorType.ParsingFailed:
            errorTypeString = "parsing_failed";
            break;
          case GeoJsonParseErrorType.AmbiguousInput:
            errorTypeString = "ambiguous_input";
            break;
          case GeoJsonParseErrorType.NoInfo:
            errorTypeString = "no_info";
            break;
          case GeoJsonParseErrorType.Other:
            errorTypeString = "other";
            break;
          default:
            // Should not happen if BAML types are aligned
            console.warn(`${logPrefix} Unknown BAML errorType: ${bamlOutput.errorType}`);
            errorTypeString = "other"; 
        }
      }
      
      results.push({
        orderId: bamlOutput.orderId,
        tipAmount: bamlOutput.tipAmount ?? null, // Ensure undefined from BAML maps to null for Zod
        explicitZeroTipDetected: bamlOutput.explicitZeroTipDetected ?? false, // BAML bools are not optional by default
        explicitDndInstructionDetected: bamlOutput.explicitDndInstructionDetected ?? false, // BAML bools are not optional by default
        note: bamlOutput.note,
        error: bamlOutput.error,
        errorType: errorTypeString,
      });

    } catch (e: any) {
      console.error(`${logPrefix} Error calling BAML function or processing its result for feature text "${featurePropertiesText.substring(0,100)}...":`, e);
      // Conform to the GeoJsonParseOutputSchema for errors
      results.push({
        orderId: null,
        tipAmount: null,
        explicitZeroTipDetected: false,
        explicitDndInstructionDetected: false,
        note: null,
        error: e.message || "BAML processing failed for item",
        errorType: "other", // Or try to infer from BamlValidationError if possible
      });
    }
  }

  if (results.length !== expectedResultsCount) {
    console.warn(`${logPrefix} Processed items count (${results.length}) does not match expected (${expectedResultsCount}). This might indicate an issue.`);
  }
  
  console.log(`${logPrefix} BAML batch processing complete. Total items processed: ${results.length}.`);
  return results;
}

// --- Remove or comment out the old single-feature function ---
/*
export async function _extractGeoJsonDataWithLLM(
  // ... old implementation ...
): Promise<z.infer<typeof GeoJsonParseOutputSchema>> {
  // ... old implementation ...
} 
*/

// --- Deprecate/Placeholder the old flow definition ---
// This flow is no longer the primary way to call the LLM logic.
// The batch processing will be orchestrated from process-uploaded-geojson.ts
/*
export const parseGeoJsonProperties = defineFlow(
  {
    name: 'parseGeoJsonProperties',
    inputSchema: GeoJsonPropertiesInputSchema, // Still uses the old single input schema
    outputSchema: GeoJsonParseOutputSchema, // Expects a single output
  },
  async (input: z.infer<typeof GeoJsonPropertiesInputSchema>) => {
     console.warn("[parseGeoJsonProperties Flow] This flow is deprecated. GeoJSON parsing is now primarily handled via batch processing in 'processUploadedGeoJsonFile'. This call will attempt a single-item batch as a fallback, but this usage is not recommended.");
     
     // Construct a GeoJsonFeature-like object for the batch function
     // This is a minimal mock since we don't have full feature context here.
     const mockFeature: GeoJsonFeature = {
        type: 'Feature',
        geometry: { type: 'Point', coordinates: [0,0] }, // Mock geometry
        properties: {
            name: input.nameProperty,
            description: input.descriptionProperty
        }
     };

     const results = await _extractGeoJsonDataBatchLLM([mockFeature], { temperature: 0.2 });
     if (results && results.length > 0) {
        return results[0]; // Return the first (and only) result
     } else {
        return {
            orderId: null, tipAmount: null, note: null, 
            explicitZeroTipDetected: false, explicitDndInstructionDetected: false,
            error: "Batch processing for single deprecated call failed or returned empty.", 
            errorType: "other"
        };
     }
  }
);
*/

// Example of how this flow might be called (e.g., in a test or another flow)
// Test function would need significant updates to test the batch function
