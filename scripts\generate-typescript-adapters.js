// generate-typescript-adapters.js
// Windows-compatible generator for TypeScript adapters from Kotlin files
const fs = require('fs');
const path = require('path');

class WindowsKotlinToTypeScriptGenerator {
  constructor() {
    // Windows paths using your actual project structure
    this.projectRoot = 'C:\\Users\\<USER>\\AndroidStudioProjects\\Autogratuity';
    this.kotlinSourcePath = path.join(this.projectRoot, 'app\\src\\main\\java\\com\\autogratuity');
    this.outputPath = path.join(this.projectRoot, 'genkit-backend\\src\\generated');

    // IMPORTANT: Do NOT touch existing generated models
    this.existingGeneratedModels = path.join(this.projectRoot, 'genkit-backend\\src\\models\\generated');

    console.log('⚠️  SAFETY: Will NOT modify existing generated models at:', this.existingGeneratedModels);
  }

  async generateAll() {
    console.log('🚀 Starting TypeScript adapter generation...');
    console.log(`📁 Kotlin source: ${this.kotlinSourcePath}`);
    console.log(`📁 Output: ${this.outputPath}`);

    try {
      // Ensure output directory exists (separate from existing generated models)
      this.ensureDirectoryExists(this.outputPath);
      this.ensureDirectoryExists(path.join(this.outputPath, 'adapters'));
      this.ensureDirectoryExists(path.join(this.outputPath, 'implementations'));
      this.ensureDirectoryExists(path.join(this.outputPath, 'mappers'));
      this.ensureDirectoryExists(path.join(this.outputPath, 'tests'));

      // NOTE: We will IMPORT from existing generated models, not recreate them
      console.log('📋 Will import from existing generated models (not recreate)');

      // Process repositories (main focus)
      await this.processRepositories();

      // Process mappers (business logic)
      await this.processMappers();

      // Process domain models (NEW: generate TypeScript domain models)
      await this.processDomainModels();

      // Generate integration tests
      await this.generateIntegrationTests();

      console.log('✅ TypeScript adapter generation completed!');

    } catch (error) {
      console.error('❌ Generation failed:', error);
      process.exit(1);
    }
  }

  ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      console.log(`📁 Created directory: ${dirPath}`);
    }
  }

  async processRepositories() {
    console.log('🔄 Processing repositories...');

    const repositoryPath = path.join(this.kotlinSourcePath, 'domain\\repository');

    if (!fs.existsSync(repositoryPath)) {
      console.log(`⚠️  Repository path not found: ${repositoryPath}`);
      return;
    }

    const repositoryFiles = fs.readdirSync(repositoryPath)
      .filter(file => file.endsWith('.kt') && file.includes('Repository'));

    for (const file of repositoryFiles) {
      console.log(`  📄 Processing ${file}...`);

      const kotlinContent = fs.readFileSync(path.join(repositoryPath, file), 'utf8');
      const repositoryName = file.replace('.kt', '');

      // Generate interface
      const tsInterface = this.generateRepositoryInterface(kotlinContent, repositoryName);
      const interfaceOutputPath = path.join(this.outputPath, 'adapters', `${repositoryName}Adapter.ts`);
      fs.writeFileSync(interfaceOutputPath, tsInterface);

      // Generate implementation
      const tsImplementation = this.generateRepositoryImplementation(kotlinContent, repositoryName);
      const implOutputPath = path.join(this.outputPath, 'implementations', `Firestore${repositoryName.replace('Repository', '')}Repository.ts`);
      fs.writeFileSync(implOutputPath, tsImplementation);

      console.log(`    ✅ Generated ${repositoryName}Adapter.ts`);
      console.log(`    ✅ Generated Firestore implementation`);
    }
  }

  async processMappers() {
    console.log('🔄 Processing mappers...');

    const mapperPath = path.join(this.kotlinSourcePath, 'data\\mapper');

    if (!fs.existsSync(mapperPath)) {
      console.log(`⚠️  Mapper path not found: ${mapperPath}`);
      return;
    }

    const mapperFiles = fs.readdirSync(mapperPath)
      .filter(file => file.endsWith('.kt') && file.includes('Mapper'));

    for (const file of mapperFiles) {
      console.log(`  📄 Processing ${file}...`);

      const kotlinContent = fs.readFileSync(path.join(mapperPath, file), 'utf8');
      const mapperName = file.replace('.kt', '');

      const tsMapper = this.generateMapper(kotlinContent, mapperName);
      const outputPath = path.join(this.outputPath, 'mappers', `${mapperName}.ts`);
      fs.writeFileSync(outputPath, tsMapper);

      console.log(`    ✅ Generated ${mapperName}.ts`);
    }
  }

  async processDomainModels() {
    console.log('🔄 Processing domain models...');

    const domainModelPath = path.join(this.kotlinSourcePath, 'domain\\model');

    if (!fs.existsSync(domainModelPath)) {
      console.log(`⚠️  Domain model path not found: ${domainModelPath}`);
      return;
    }

    // Ensure domain models output directory exists
    const domainOutputPath = path.join(this.projectRoot, 'genkit-backend\\src\\models\\domain');
    this.ensureDirectoryExists(domainOutputPath);

    const domainModelFiles = fs.readdirSync(domainModelPath)
      .filter(file => file.endsWith('.kt') && !file.includes('Test'));

    for (const file of domainModelFiles) {
      console.log(`  📄 Processing ${file}...`);

      const kotlinContent = fs.readFileSync(path.join(domainModelPath, file), 'utf8');
      const modelName = file.replace('.kt', '');

      const tsDomainModel = this.generateDomainModel(kotlinContent, modelName);
      const outputPath = path.join(domainOutputPath, `${modelName}.ts`);
      fs.writeFileSync(outputPath, tsDomainModel);

      console.log(`    ✅ Generated ${modelName}.ts`);
    }
  }

  async generateIntegrationTests() {
    console.log('🔄 Generating integration tests...');

    const testPath = path.join(this.outputPath, 'tests');
    this.ensureDirectoryExists(testPath);

    // Generate repository integration tests
    await this.generateRepositoryTests();

    // Generate mapper consistency tests
    await this.generateMapperTests();

    // Generate business logic validation tests
    await this.generateBusinessLogicTests();

    console.log('✅ Integration tests generated');
  }

  generateImportsFromExistingModels(repositoryName) {
    // Generate imports from your existing generated models
    // This ensures we use your current schema-generated types
    const baseImports = `
import type { Result } from '../types/Result';
import type {
  Delivery as TsDeliveryInterface,
  Address as TsAddressInterface,
  User_profile as TsUserProfileInterface
} from '../../models/generated/delivery.schema';
import type {
  DeliveryStats as TsDeliveryStatsInterface
} from '../../models/generated/address.schema';`;

    // Add repository-specific imports
    if (repositoryName.includes('Delivery')) {
      return baseImports + `
import type { DeliveryData as TsDeliveryData } from '../../models/generated/delivery.schema';`;
    } else if (repositoryName.includes('Address')) {
      return baseImports + `
import type { AddressData as TsAddressData } from '../../models/generated/address.schema';`;
    } else if (repositoryName.includes('User')) {
      return baseImports + `
import type { UserProfileData as TsUserProfileData } from '../../models/generated/user_profile.schema';`;
    }

    return baseImports;
  }

  generateRepositoryInterface(kotlinContent, repositoryName) {
    const methods = this.extractMethods(kotlinContent);
    const tsMethodsText = methods.map(method => {
      const params = method.parameters.map(p => `${p.name}: ${p.type}`).join(', ');
      return `  ${method.name}(${params}): ${method.returnType};`;
    }).join('\n');

    // Import from your existing generated models (DO NOT recreate them)
    const imports = this.generateImportsFromExistingModels(repositoryName);

    return `// Auto-generated from ${repositoryName}.kt
// IMPORTS FROM EXISTING GENERATED MODELS (not recreated)
${imports}

/**
 * TypeScript adapter interface generated from ${repositoryName}.kt
 * Maintains identical contract to Kotlin implementation
 * Uses existing generated models from schemas
 */
export interface ${repositoryName}Adapter {
${tsMethodsText}
}`;
  }

  generateRepositoryImplementation(kotlinContent, repositoryName) {
    const methods = this.extractMethods(kotlinContent);
    const className = `Firestore${repositoryName.replace('Repository', '')}Repository`;
    const imports = this.generateImportsFromExistingModels(repositoryName);

    const tsMethodsText = methods.map(method => {
      const params = method.parameters.map(p => `${p.name}: ${p.type}`).join(', ');
      const methodBody = this.generateMethodImplementation(method, kotlinContent);

      return `  async ${method.name}(${params}): ${method.returnType} {
${methodBody}
  }`;
    }).join('\n\n');

    return `// Auto-generated from ${repositoryName}.kt
// IMPORTS FROM EXISTING GENERATED MODELS
${imports}
import { ${repositoryName}Adapter } from '../adapters/${repositoryName}Adapter';
import { FirebaseFirestore } from 'firebase-admin/firestore';

/**
 * Firestore implementation generated from Kotlin patterns
 * Uses existing generated models and cloud function utilities
 */
export class ${className} implements ${repositoryName}Adapter {
  constructor(
    private firestore: FirebaseFirestore.Firestore
  ) {}

${tsMethodsText}
}`;
  }

  generateMapper(kotlinContent, mapperName) {
    // Extract business logic functions from your Kotlin mapper
    const functions = this.extractMapperFunctions(kotlinContent);

    const tsFunctionsText = functions.map(func => {
      return `  ${func.name}(${func.parameters}): ${func.returnType} {
    // TODO: Port business logic from Kotlin ${mapperName}.${func.name}
    throw new Error('${func.name} not yet implemented');
  }`;
    }).join('\n\n');

    return `// Auto-generated from ${mapperName}.kt
import { Result } from '../types/Result';
import { Delivery } from '../models/domain/Delivery';

/**
 * Business logic mapper generated from Kotlin ${mapperName}
 */
export class ${mapperName} {
${tsFunctionsText}
}`;
  }

  generateDomainModel(kotlinContent, modelName) {
    // Extract data class properties
    const properties = this.extractDataClassProperties(kotlinContent);

    // Extract nested classes/enums
    const nestedTypes = this.extractNestedTypes(kotlinContent);

    const tsPropertiesText = properties.map(prop => {
      return `  ${prop.name}${prop.optional ? '?' : ''}: ${prop.type};`;
    }).join('\n');

    // Generate nested types first
    const nestedTypesText = nestedTypes.map(nested => {
      if (nested.type === 'enum') {
        const enumValues = nested.values.map(val => `  ${val} = '${val}'`).join(',\n');
        return `export enum ${nested.name} {
${enumValues}
}`;
      } else if (nested.type === 'dataClass') {
        const nestedProps = nested.properties.map(prop => {
          return `  ${prop.name}${prop.optional ? '?' : ''}: ${prop.type};`;
        }).join('\n');
        return `export interface ${nested.name} {
${nestedProps}
}`;
      }
      return '';
    }).filter(t => t).join('\n\n');

    const mainInterface = `/**
 * Domain model generated from Kotlin ${modelName}
 * Auto-generated TypeScript equivalent of Android domain model
 */
export interface ${modelName} {
${tsPropertiesText}
}`;

    return `// Auto-generated from ${modelName}.kt
// TypeScript equivalent of Android domain.model.${modelName}

${nestedTypesText}${nestedTypesText ? '\n\n' : ''}${mainInterface}`;
  }

  generateDTO(kotlinContent, dtoName) {
    // Similar to domain model but for DTOs
    const properties = this.extractDataClassProperties(kotlinContent);

    const tsPropertiesText = properties.map(prop => {
      return `  ${prop.name}${prop.optional ? '?' : ''}: ${prop.type};`;
    }).join('\n');

    return `// Auto-generated from ${dtoName}.kt

/**
 * DTO generated from Kotlin ${dtoName}
 */
export interface ${dtoName} {
${tsPropertiesText}
}`;
  }

  extractMethods(kotlinContent) {
    // Simple method extraction - you can enhance this
    const methods = [];
    const lines = kotlinContent.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.startsWith('suspend fun ') || trimmed.startsWith('fun ')) {
        const method = this.parseKotlinMethod(trimmed);
        if (method) methods.push(method);
      }
    }

    return methods;
  }

  parseKotlinMethod(methodLine) {
    // Parse Kotlin method signature to TypeScript equivalent
    const suspendMatch = methodLine.match(/(?:suspend )?fun (\w+)\((.*?)\): (.*)/);
    if (!suspendMatch) return null;

    const name = suspendMatch[1];
    const paramsStr = suspendMatch[2];
    const returnType = this.kotlinToTypeScript(suspendMatch[3]);

    const parameters = paramsStr ? paramsStr.split(',').map(param => {
      const paramParts = param.trim().split(':');
      if (paramParts.length < 2) {
        console.warn(`⚠️  Skipping malformed parameter: ${param}`);
        return null;
      }
      const paramName = paramParts[0].trim();
      const paramType = paramParts[1].trim();
      return {
        name: paramName,
        type: this.kotlinToTypeScript(paramType)
      };
    }).filter(p => p !== null) : [];

    return { name, parameters, returnType };
  }

  extractMapperFunctions(kotlinContent) {
    // Extract business logic functions from mapper
    const functions = [];
    const lines = kotlinContent.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.startsWith('fun ') && !trimmed.includes('suspend')) {
        // Extract non-suspend functions (business logic)
        const func = this.parseKotlinMethod(trimmed);
        if (func) functions.push(func);
      }
    }

    return functions;
  }

  extractDataClassProperties(kotlinContent) {
    // Extract properties from data class
    const properties = [];
    const lines = kotlinContent.split('\n');
    let inDataClass = false;

    for (const line of lines) {
      const trimmed = line.trim();

      if (trimmed.startsWith('data class ')) {
        inDataClass = true;
        continue;
      }

      if (inDataClass && trimmed.includes(':')) {
        const propMatch = trimmed.match(/val (\w+):\s*([^,\)]+)/);
        if (propMatch) {
          properties.push({
            name: propMatch[1],
            type: this.kotlinToTypeScript(propMatch[2].trim()),
            optional: propMatch[2].includes('?')
          });
        }
      }

      if (trimmed === ')') {
        inDataClass = false;
      }
    }

    return properties;
  }

  extractNestedTypes(kotlinContent) {
    // Extract nested enums and data classes
    const nestedTypes = [];
    const lines = kotlinContent.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const trimmed = lines[i].trim();

      // Extract enums
      if (trimmed.startsWith('enum class ')) {
        const enumMatch = trimmed.match(/enum class (\w+)/);
        if (enumMatch) {
          const enumName = enumMatch[1];
          const enumValues = [];

          // Look for enum values
          for (let j = i + 1; j < lines.length; j++) {
            const enumLine = lines[j].trim();
            if (enumLine === '}') break;

            const valueMatch = enumLine.match(/(\w+)[,;]?/);
            if (valueMatch && !enumLine.includes('(') && !enumLine.includes('fun ')) {
              enumValues.push(valueMatch[1]);
            }
          }

          nestedTypes.push({
            name: enumName,
            type: 'enum',
            values: enumValues
          });
        }
      }

      // Extract nested data classes
      if (trimmed.includes('data class ') && !trimmed.startsWith('data class ')) {
        const nestedMatch = trimmed.match(/data class (\w+)/);
        if (nestedMatch) {
          const nestedName = nestedMatch[1];
          const nestedProperties = [];

          // Extract properties for nested class
          for (let j = i + 1; j < lines.length; j++) {
            const nestedLine = lines[j].trim();
            if (nestedLine === ')') break;

            const propMatch = nestedLine.match(/val (\w+):\s*([^,\)]+)/);
            if (propMatch) {
              nestedProperties.push({
                name: propMatch[1],
                type: this.kotlinToTypeScript(propMatch[2].trim()),
                optional: propMatch[2].includes('?')
              });
            }
          }

          nestedTypes.push({
            name: nestedName,
            type: 'dataClass',
            properties: nestedProperties
          });
        }
      }
    }

    return nestedTypes;
  }

  kotlinToTypeScript(kotlinType) {
    // Handle undefined/null types
    if (!kotlinType || kotlinType.trim() === '') {
      return 'any';
    }

    const typeMap = {
      // Basic types
      'String': 'string',
      'Int': 'number',
      'Long': 'number',
      'Double': 'number',
      'Float': 'number',
      'Boolean': 'boolean',

      // Date/Time types
      'OffsetDateTime': 'Date',
      'LocalDateTime': 'Date',
      'Instant': 'Date',
      'Timestamp': 'Date',

      // Collections
      'List<String>': 'string[]',
      'List<Int>': 'number[]',
      'List<Long>': 'number[]',
      'List<Double>': 'number[]',
      'List<Boolean>': 'boolean[]',
      'MutableList<String>': 'string[]',
      'Array<String>': 'string[]',
      'Set<String>': 'string[]',

      // Maps
      'Map<String, String>': 'Record<string, string>',
      'Map<String, Int>': 'Record<string, number>',
      'Map<String, Long>': 'Record<string, number>',
      'Map<String, Double>': 'Record<string, number>',
      'Map<String, Boolean>': 'Record<string, boolean>',
      'Map<String, Any>': 'Record<string, any>',
      'MutableMap<String, Any>': 'Record<string, any>',

      // Repository/Flow types
      'Result<Delivery?>': 'Promise<Result<Delivery | null>>',
      'Result<List<Delivery>>': 'Promise<Result<Delivery[]>>',
      'Result<Unit>': 'Promise<Result<void>>',
      'Result<String>': 'Promise<Result<string>>',
      'Flow<Result<Delivery?>>': 'Observable<Result<Delivery | null>>',

      // Domain-specific types (keep as-is for domain models)
      'DeliveryDetails': 'DeliveryDetails',
      'AddressComponents': 'AddressComponents',
      'UserSubscription': 'UserSubscription',
      'UserPreferences': 'UserPreferences',
      'UserPermissions': 'UserPermissions',
      'UserUsage': 'UserUsage',
      'UserSyncInfo': 'UserSyncInfo',
      'UserCommunication': 'UserCommunication',
      'UserUsageStats': 'UserUsageStats',
      'CustomDndRule': 'CustomDndRule',
      'ComparisonType': 'ComparisonType',
      'ManualDndState': 'ManualDndState'
    };

    let mappedType = kotlinType.trim();

    // Handle nullable types
    const isNullable = mappedType.endsWith('?');
    if (isNullable) {
      mappedType = mappedType.slice(0, -1);
    }

    // Apply type mappings
    for (const [kotlin, typescript] of Object.entries(typeMap)) {
      mappedType = mappedType.replace(new RegExp(kotlin.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), typescript);
    }

    // Handle generic List types not covered above
    mappedType = mappedType.replace(/List<([^>]+)>/g, '$1[]');
    mappedType = mappedType.replace(/MutableList<([^>]+)>/g, '$1[]');
    mappedType = mappedType.replace(/Array<([^>]+)>/g, '$1[]');
    mappedType = mappedType.replace(/Set<([^>]+)>/g, '$1[]');

    // Add null union for nullable types
    if (isNullable) {
      mappedType = `${mappedType} | null`;
    }

    return mappedType;
  }

  extractKotlinMethodBody(kotlinContent, methodName) {
    // Find the method and extract its body
    const lines = kotlinContent.split('\n');
    let methodStartIndex = -1;
    let braceCount = 0;
    let methodBody = [];
    let inMethod = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Find method declaration
      if (line.includes(`fun ${methodName}(`) && !inMethod) {
        methodStartIndex = i;
        inMethod = true;

        // Check if method body starts on same line
        if (line.includes('{')) {
          braceCount = (line.match(/\{/g) || []).length - (line.match(/\}/g) || []).length;
          if (braceCount > 0) {
            methodBody.push(line.substring(line.indexOf('{') + 1));
          }
        }
        continue;
      }

      if (inMethod) {
        if (line.includes('{')) {
          braceCount += (line.match(/\{/g) || []).length;
        }
        if (line.includes('}')) {
          braceCount -= (line.match(/\}/g) || []).length;
        }

        if (braceCount > 0) {
          methodBody.push(line);
        } else if (braceCount === 0) {
          // Method ended
          break;
        }
      }
    }

    return methodBody.length > 0 ? methodBody.join('\n') : null;
  }

  convertKotlinToTypeScript(kotlinBody, method) {
    // Convert Kotlin syntax to TypeScript
    let tsBody = kotlinBody;

    // Convert common Kotlin patterns to TypeScript
    const conversions = [
      // Coroutines and async
      [/withContext\(Dispatchers\.IO\)\s*\{/, 'try {'],
      [/suspend\s+fun/, 'async'],

      // Firestore operations
      [/firestore\.collection\("([^"]+)"\)\.document\("([^"]+)"\)/g, 'this.firestore.collection("$1").doc("$2")'],
      [/firestore\.collection\("([^"]+)"\)\.document\(([^)]+)\)/g, 'this.firestore.collection("$1").doc($2)'],
      [/\.document\(/g, '.doc('],
      [/\.update\(/g, '.update('],
      [/\.set\(/g, '.set('],
      [/\.get\(\)/g, '.get()'],

      // Result patterns
      [/Result\.success\(Unit\)/g, 'Result.success(undefined)'],
      [/Result\.success\(/g, 'Result.success('],
      [/Result\.error\(/g, 'Result.error('],

      // Exception handling
      [/catch\s*\(\s*e:\s*Exception\s*\)/g, 'catch (e)'],
      [/throw\s+([^;]+)/g, 'throw $1'],

      // Variable declarations
      [/val\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=/g, 'const $1 ='],
      [/var\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=/g, 'let $1 ='],

      // Null checks and safe calls
      [/([a-zA-Z_][a-zA-Z0-9_]*)\?\./g, '$1?.'],
      [/([a-zA-Z_][a-zA-Z0-9_]*)\s*!!/g, '$1!'],

      // Collections
      [/listOf\(/g, '['],
      [/mapOf\(/g, '{'],
      [/\.isEmpty\(\)/g, '.length === 0'],
      [/\.isNotEmpty\(\)/g, '.length > 0'],
      [/\.any\s*\{([^}]+)\}/g, '.some($1)'],
      [/\.filter\s*\{([^}]+)\}/g, '.filter($1)'],
      [/\.map\s*\{([^}]+)\}/g, '.map($1)'],

      // String operations
      [/\.toString\(\)/g, '.toString()'],
      [/\.toDouble\(\)/g, 'parseFloat()'],
      [/\.toInt\(\)/g, 'parseInt()'],

      // Conditional expressions
      [/if\s*\(([^)]+)\)\s*\{/g, 'if ($1) {'],
      [/else\s*if\s*\(([^)]+)\)\s*\{/g, 'else if ($1) {'],
      [/else\s*\{/g, 'else {'],

      // Return statements
      [/return@withContext/g, 'return'],

      // Await async operations
      [/\.get\(\)/g, 'await .get()'],
      [/\.set\(/g, 'await .set('],
      [/\.update\(/g, 'await .update('],
      [/\.delete\(\)/g, 'await .delete()'],
    ];

    // Apply conversions
    conversions.forEach(([pattern, replacement]) => {
      tsBody = tsBody.replace(pattern, replacement);
    });

    // Add proper indentation and try-catch if not present
    const lines = tsBody.split('\n');
    const indentedLines = lines.map(line => `    ${line.trim()}`).filter(line => line.trim());

    // Wrap in try-catch if not already present
    if (!tsBody.includes('try {')) {
      return `    try {
${indentedLines.join('\n')}
    } catch (error) {
      return Result.error(error as Error);
    }`;
    }

    return indentedLines.join('\n');
  }

  generateMethodImplementation(method, kotlinContent) {
    // Extract actual Kotlin method body and convert it
    const kotlinMethodBody = this.extractKotlinMethodBody(kotlinContent, method.name);

    if (kotlinMethodBody) {
      return this.convertKotlinToTypeScript(kotlinMethodBody, method);
    }

    // Fallback to pattern-based generation
    if (method.name.includes('ById')) {
      return this.generateGetByIdImplementation();
    } else if (method.name.startsWith('add')) {
      return this.generateAddImplementation();
    } else if (method.name.startsWith('update')) {
      return this.generateUpdateImplementation();
    } else {
      return `    // TODO: Implement ${method.name}
    throw new Error('Method ${method.name} not yet implemented');`;
    }
  }

  generateGetByIdImplementation() {
    return `    try {
      // Use existing Firestore structure from Kotlin implementation
      const doc = await this.firestore
        .collection('users').doc(userId)
        .collection('user_deliveries').doc(id)
        .get();

      if (!doc.exists) {
        return Result.success(null);
      }

      // Use existing utilities and mapper
      const dto = documentSnapshotToDeliveryDto(doc);
      return await this.mapper.mapToDomain(dto.id, dto.deliveryData);
    } catch (error) {
      return Result.error(error as Error);
    }`;
  }

  generateAddImplementation() {
    return `    try {
      const dtoResult = await this.mapper.mapToDtoData(delivery);
      if (dtoResult.isError()) return dtoResult;

      const deliveryRef = this.firestore
        .collection('users').doc(delivery.details.userId)
        .collection('user_deliveries').doc();

      await deliveryRef.set({ deliveryData: dtoResult.data });
      return Result.success(deliveryRef.id);
    } catch (error) {
      return Result.error(error as Error);
    }`;
  }

  generateUpdateImplementation() {
    return `    try {
      const dtoResult = await this.mapper.mapToDto(delivery);
      if (dtoResult.isError()) return dtoResult;

      const deliveryRef = this.firestore
        .collection('users').doc(delivery.details.userId)
        .collection('user_deliveries').doc(delivery.id);

      await deliveryRef.update({ deliveryData: dtoResult.data.deliveryData });
      return Result.success(undefined);
    } catch (error) {
      return Result.error(error as Error);
    }`;
  }

  async generateRepositoryTests() {
    console.log('  📄 Generating repository integration tests...');

    const repositoryPath = path.join(this.kotlinSourcePath, 'domain\\repository');
    if (!fs.existsSync(repositoryPath)) return;

    const repositoryFiles = fs.readdirSync(repositoryPath)
      .filter(file => file.endsWith('.kt') && file.includes('Repository'));

    for (const file of repositoryFiles) {
      const repositoryName = file.replace('.kt', '');
      const testContent = this.generateRepositoryTestContent(repositoryName);
      const testPath = path.join(this.outputPath, 'tests', `${repositoryName}.integration.test.ts`);
      fs.writeFileSync(testPath, testContent);
      console.log(`    ✅ Generated ${repositoryName} integration tests`);
    }
  }

  async generateMapperTests() {
    console.log('  📄 Generating mapper consistency tests...');

    const mapperPath = path.join(this.kotlinSourcePath, 'data\\mapper');
    if (!fs.existsSync(mapperPath)) return;

    const mapperFiles = fs.readdirSync(mapperPath)
      .filter(file => file.endsWith('.kt') && file.includes('Mapper'));

    for (const file of mapperFiles) {
      const mapperName = file.replace('.kt', '');
      const testContent = this.generateMapperTestContent(mapperName);
      const testPath = path.join(this.outputPath, 'tests', `${mapperName}.consistency.test.ts`);
      fs.writeFileSync(testPath, testContent);
      console.log(`    ✅ Generated ${mapperName} consistency tests`);
    }
  }

  async generateBusinessLogicTests() {
    console.log('  📄 Generating business logic validation tests...');

    const testContent = this.generateBusinessLogicTestContent();
    const testPath = path.join(this.outputPath, 'tests', 'BusinessLogic.validation.test.ts');
    fs.writeFileSync(testPath, testContent);
    console.log(`    ✅ Generated business logic validation tests`);
  }

  generateRepositoryTestContent(repositoryName) {
    return `// Auto-generated integration tests for ${repositoryName}
import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';
import { ${repositoryName}Adapter } from '../adapters/${repositoryName}Adapter';
import { Firestore${repositoryName.replace('Repository', '')}Repository } from '../implementations/Firestore${repositoryName.replace('Repository', '')}Repository';

describe('${repositoryName} Integration Tests', () => {
  let repository: ${repositoryName}Adapter;

  beforeAll(async () => {
    // Initialize with test Firestore instance
    repository = new Firestore${repositoryName.replace('Repository', '')}Repository(testFirestore);
  });

  test('createDelivery: Android vs Cloud consistency', async () => {
    // TODO: Implement test that calls both Android and Cloud versions
    // and validates they produce identical results
    expect(true).toBe(true); // Placeholder
  });

  test('business logic consistency validation', async () => {
    // TODO: Test that business logic (DND rules, calculations)
    // produces same results as Android implementation
    expect(true).toBe(true); // Placeholder
  });

  afterAll(async () => {
    // Cleanup test data
  });
});`;
  }

  generateMapperTestContent(mapperName) {
    return `// Auto-generated consistency tests for ${mapperName}
import { describe, test, expect } from '@jest/globals';
import { ${mapperName} } from '../mappers/${mapperName}';

describe('${mapperName} Consistency Tests', () => {
  let mapper: ${mapperName};

  beforeAll(() => {
    mapper = new ${mapperName}();
  });

  test('business logic matches Android implementation', async () => {
    // TODO: Test that mapper business logic produces same results
    // as Android ${mapperName}
    expect(true).toBe(true); // Placeholder
  });

  test('data transformation consistency', async () => {
    // TODO: Test that data transformations match Android patterns
    expect(true).toBe(true); // Placeholder
  });
});`;
  }

  generateBusinessLogicTestContent() {
    return `// Auto-generated business logic validation tests
import { describe, test, expect } from '@jest/globals';

describe('Business Logic Validation', () => {
  test('DND evaluation consistency', async () => {
    // TODO: Test that DND evaluation logic matches Android
    // Test cases: $0 tips, custom rules, manual overrides
    expect(true).toBe(true); // Placeholder
  });

  test('tip calculation consistency', async () => {
    // TODO: Test that tip calculations match Android
    // Test cases: percentages, rounding, edge cases
    expect(true).toBe(true); // Placeholder
  });

  test('address validation consistency', async () => {
    // TODO: Test that address validation matches Android
    expect(true).toBe(true); // Placeholder
  });
});`;
  }
}

// Main execution
async function main() {
  const generator = new WindowsKotlinToTypeScriptGenerator();
  await generator.generateAll();
}

// Handle command line execution
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { WindowsKotlinToTypeScriptGenerator };