package com.autogratuity.data.util

import android.util.Log
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.Query
import com.google.firebase.firestore.QuerySnapshot
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Modern Firestore page loader using coroutines and Result-based error handling.
 * Replaces legacy RxJava patterns with 2025 Kotlin standards including:
 * - Suspend functions instead of RxJava Single
 * - Result-based error handling
 * - Structured exception handling
 * - Modern Kotlin coroutines patterns
 *
 * @param T The type of data items to load
 */
class FlowFirestorePageLoader<T : Any>(
    private val baseQuery: Query,
    private val mapper: (QuerySnapshot) -> List<T>
) : FlowPageLoader<T, DocumentSnapshot> {

    companion object {
        private const val TAG = "FlowFirestorePageLoader"
    }

    override suspend fun loadPage(pageSize: Int, pageKey: DocumentSnapshot?): Result<PageResult<T, DocumentSnapshot>> {
        return try {
            var pageQuery = baseQuery.limit(pageSize.toLong())
            pageKey?.let { key ->
                pageQuery = pageQuery.startAfter(key)
            }

            Log.d(TAG, "Loading page with size: $pageSize, key: ${pageKey?.id}")

            val querySnapshot = pageQuery.get().await()
            
            val items = try {
                mapper(querySnapshot)
            } catch (e: Exception) {
                Log.e(TAG, "Error mapping query snapshot", e)
                return Result.failure(FirestorePageLoaderError.MappingError(e))
            }

            val nextPageKey: DocumentSnapshot? = if (querySnapshot.documents.isNotEmpty()) {
                querySnapshot.documents.last()
            } else {
                null
            }

            Log.d(TAG, "Successfully loaded ${items.size} items, hasNext: ${nextPageKey != null}")
            
            Result.success(PageResult(items, nextPageKey))
            
        } catch (e: Exception) {
            Log.e(TAG, "Error loading page from Firestore", e)
            Result.failure(FirestorePageLoaderError.QueryError(e))
        }
    }
}

/**
 * Firestore page loader errors
 */
sealed class FirestorePageLoaderError : Exception() {
    data class QueryError(override val cause: Throwable) : FirestorePageLoaderError()
    data class MappingError(override val cause: Throwable) : FirestorePageLoaderError()
    object InvalidPageSize : FirestorePageLoaderError()
}

/**
 * Factory for creating Firestore page loaders
 */
@Singleton
class FirestorePageLoaderFactory @Inject constructor() {
    
    /**
     * Create a page loader for a specific Firestore query
     */
    fun <T : Any> create(
        baseQuery: Query,
        mapper: (QuerySnapshot) -> List<T>
    ): FlowFirestorePageLoader<T> {
        return FlowFirestorePageLoader(baseQuery, mapper)
    }
    
    /**
     * Create a page loader with object mapping
     */
    inline fun <reified T : Any> createWithMapping(
        baseQuery: Query
    ): FlowFirestorePageLoader<T> {
        return FlowFirestorePageLoader(baseQuery) { querySnapshot ->
            querySnapshot.toObjects(T::class.java)
        }
    }
}

/**
 * Legacy compatibility class for gradual migration
 * @deprecated Use FlowFirestorePageLoader instead
 */
@Deprecated(
    message = "Use FlowFirestorePageLoader with coroutines instead",
    replaceWith = ReplaceWith("FlowFirestorePageLoader"),
    level = DeprecationLevel.WARNING
)
class FirestorePageLoader<T : Any>(
    private val baseQuery: Query,
    private val mapper: (QuerySnapshot) -> List<T>
) : FlowPageLoader<T, DocumentSnapshot> {

    companion object {
        private const val TAG = "FirestorePageLoader"
    }

    // Implement the abstract method from FlowPageLoader interface
    override suspend fun loadPage(pageSize: Int, pageKey: DocumentSnapshot?): Result<PageResult<T, DocumentSnapshot>> {
        return try {
            var pageQuery = baseQuery.limit(pageSize.toLong())
            pageKey?.let { key ->
                pageQuery = pageQuery.startAfter(key)
            }

            val querySnapshot = pageQuery.get().await()

            val items = try {
                mapper(querySnapshot)
            } catch (e: Exception) {
                return Result.failure(FirestorePageLoaderError.MappingError(e))
            }

            val nextPageKey: DocumentSnapshot? = if (querySnapshot.documents.isNotEmpty()) {
                querySnapshot.documents.last()
            } else {
                null
            }

            Result.success(PageResult(items, nextPageKey))

        } catch (e: Exception) {
            Result.failure(FirestorePageLoaderError.QueryError(e))
        }
    }

    @Deprecated("Use FlowFirestorePageLoader.loadPage() instead")
    fun loadPageRx(pageSize: Int, pageKey: DocumentSnapshot?): io.reactivex.rxjava3.core.Single<PageResult<T, DocumentSnapshot>> {
        return io.reactivex.rxjava3.core.Single.create { emitter ->
            var pageQuery = baseQuery.limit(pageSize.toLong())
            pageKey?.let {
                pageQuery = pageQuery.startAfter(it)
            }

            pageQuery.get()
                .addOnSuccessListener { querySnapshot ->
                    try {
                        val items = mapper(querySnapshot)
                        val nextPageKey: DocumentSnapshot? = if (querySnapshot.documents.isNotEmpty()) {
                            querySnapshot.documents.last()
                        } else {
                            null
                        }
                        emitter.onSuccess(PageResult(items, nextPageKey))
                    } catch (e: Throwable) {
                        Log.e(TAG, "Error mapping query snapshot", e)
                        emitter.onError(e)
                    }
                }
                .addOnFailureListener { e ->
                    Log.e(TAG, "Error loading page from Firestore", e)
                    emitter.onError(e)
                }
        }
    }
} 