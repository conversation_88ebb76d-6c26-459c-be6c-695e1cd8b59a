import * as z from 'zod';
import { defineFlow } from '@genkit-ai/flow';
import { getFirestore, Timestamp, FieldValue } from 'firebase-admin/firestore';
import { dndPreferencesCache, ComputedDndSettings } from '../utils/dnd-preferences-cache';

// ✅ PERFORMANCE: Initialize Firestore connection at module load to reduce cold start impact
const db = getFirestore();

// ✅ SIMPLIFIED IMPORTS: Use generated types directly
import type {
  Address,
  DeliveryStats,
  Flags
} from '../models/generated/address.schema';

import type {
  Delivery
} from '../models/generated/delivery.schema';

import type {
  Status
} from '../models/generated/status.schema';

// ✅ SIMPLIFIED: Basic runtime validation with graceful degradation
function validateWithFallback<T>(data: any, entityType: string): { data: T; errors: string[] } {
  const errors: string[] = [];

  if (!data) {
    errors.push(`${entityType} data is null/undefined`);
    return { data: {} as T, errors };
  }

  // Continue with data, log errors but don't fail
  return { data: data as T, errors };
}

// ✅ SIMPLIFIED: Basic address validation with graceful degradation
export function validateAndCastAddress(data: any): Address | null {
  const validation = validateWithFallback<Address>(data, 'Address');
  if (validation.errors.length > 0) {
    console.warn('Address validation warnings:', validation.errors);
  }
  return validation.data;
}

// ✅ SIMPLIFIED: Basic delivery validation with graceful degradation
export function validateAndCastDelivery(data: any, docId?: string): Delivery | null {
  const deliveryData = data?.deliveryData || data;
  const validation = validateWithFallback<Delivery>({ deliveryData }, 'Delivery');
  if (validation.errors.length > 0) {
    console.warn('Delivery validation warnings:', validation.errors);
  }
  return {
    ...validation.data,
    id: docId || validation.data.id || ''
  } as Delivery;
}

export function validateAndCastFlags(data: any): Flags | null {
  const validation = validateWithFallback<Flags>(data, 'Flags');
  if (validation.errors.length > 0) {
    console.warn('Flags validation warnings:', validation.errors);
  }
  return validation.data;
}

export function validateAndCastStatus(data: any): Status | null {
  const validation = validateWithFallback<Status>(data, 'Status');
  if (validation.errors.length > 0) {
    console.warn('Status validation warnings:', validation.errors);
  }
  return validation.data;
}




const FlowOutputSchema = z.object({
  addressId: z.string().nullable(),
  status: z.string(),
  updatedStats: z.any().nullable(),
});

// ✅ SIMPLIFIED: Basic timestamp parsing
function parseTimestamp(timestampInput: any): Timestamp | null {
  if (!timestampInput) return null;
  if (timestampInput instanceof Timestamp) return timestampInput;
  if (timestampInput && typeof timestampInput.toDate === 'function') {
    return Timestamp.fromDate(timestampInput.toDate());
  }
  return null;
}

export const AddressStatsUpdateInputSchema = z.object({
  userId: z.string(),
  addressId: z.string(),
  deliveryId: z.string().optional(),
});

// ✅ SIMPLIFIED: Calculate address stats using simple aggregation
function calculateAddressStats(deliveries: Delivery[]): {
  deliveryCount: number;
  tipCount: number;
  pendingCount: number;
  totalTips: number;
  highestTip: number | null;
  lastDeliveryTimestamp: Timestamp | null;
  averageTipAmount: number;
  collectedOrderIds: string[];
} {
  let deliveryCount = 0;
  let tipCount = 0;
  let pendingCount = 0;
  let totalTips = 0;
  let highestTip: number | null = null;
  let lastDeliveryTimestamp: Timestamp | null = null;
  const collectedOrderIds: string[] = [];

  for (const delivery of deliveries) {
    deliveryCount++;
    collectedOrderIds.push(delivery.deliveryData.orderId);

    const tipAmount = delivery.deliveryData?.amounts?.tipAmount;
    const isCompleted = delivery.deliveryData?.status?.isCompleted;

    if (tipAmount != null && isCompleted) {
      tipCount++;
      totalTips += tipAmount;
      if (highestTip === null || tipAmount > highestTip) {
        highestTip = tipAmount;
      }
    } else {
      pendingCount++;
    }

    // Track latest timestamp
    const timestamp = delivery.deliveryData?.times?.completedAt || delivery.deliveryData?.metadata?.createdAt;
    if (timestamp && (!lastDeliveryTimestamp || timestamp.toMillis() > lastDeliveryTimestamp.toMillis())) {
      lastDeliveryTimestamp = timestamp;
    }
  }

  return {
    deliveryCount,
    tipCount,
    pendingCount,
    totalTips,
    averageTipAmount: tipCount > 0 ? totalTips / tipCount : 0,
    highestTip,
    lastDeliveryTimestamp,
    collectedOrderIds
  };
}

// ✅ SIMPLIFIED: DND evaluation using existing architecture
function evaluateAddressDnd(
  deliveries: Delivery[],
  userDndPrefs: ComputedDndSettings,
  manualState: string | null
): { dnd: boolean; source: string | null } {
  // 1. Manual override (highest priority)
  if (manualState === 'FORCE_DND') return { dnd: true, source: 'MANUAL_USER_FORCE_DND' };
  if (manualState === 'FORCE_ALLOW') return { dnd: false, source: 'MANUAL_USER_FORCE_ALLOW' };

  // 2. Use existing DND evaluation logic
  const deliveryDataArray = deliveries.map(d => d.deliveryData);
  const result = evaluateDndForDeliveryData(deliveryDataArray, userDndPrefs);

  if (result.dnd) {
    return {
      dnd: true,
      source: result.reason === 'EXPLICIT_IMPORT' ? 'RULE_BASED_EXPLICIT_IMPORT' : 'RULE_BASED_USER_PREFERENCES'
    };
  }

  return { dnd: false, source: null };
}

// ✅ SIMPLIFIED: DND evaluation using existing architecture
export function evaluateDndForDeliveryData(
    allDeliveryData: Delivery['deliveryData'][],
    userDndPrefs: ComputedDndSettings,
    hasIncompleteHistory = false
): { dnd: boolean; reason: "EXPLICIT_IMPORT" | "RULE_BASED" | null } {
    if (allDeliveryData.length === 0) {
        return { dnd: false, reason: null };
    }

    // 1. Check for explicit import DND flag
    const hasExplicitImportDnd = allDeliveryData.some(deliveryData => {
        const status = validateAndCastStatus(deliveryData.status);
        return status?.dndReason === 'EXPLICIT_IMPORT';
    });

    if (hasExplicitImportDnd) {
        return { dnd: true, reason: 'EXPLICIT_IMPORT' };
    }

    // 2. Analyze confirmed deliveries (exclude pending)
    const confirmedDeliveries = allDeliveryData.filter(deliveryData => {
        const tipAmount = deliveryData.amounts?.tipAmount ?? null;
        const status = validateAndCastStatus(deliveryData.status);
        const isCompleted = status?.isCompleted ?? false;

        // Only confirmed deliveries (tipAmount set AND completed)
        return tipAmount != null && isCompleted;
    });

    if (confirmedDeliveries.length === 0) {
        return { dnd: false, reason: null };
    }

    // 3. Apply DND rules
    if (userDndPrefs.isPremiumUser && userDndPrefs.customRule?.isEnabled) {
        // Premium users: Custom threshold
        const threshold = userDndPrefs.customRule.tipAmountThreshold;
        const lowTipCount = confirmedDeliveries.filter(d => (d.amounts?.tipAmount ?? 0) <= threshold).length;

        if (confirmedDeliveries.length >= 2 && lowTipCount >= 1) {
            return { dnd: true, reason: 'RULE_BASED' };
        }
    } else if (userDndPrefs.defaultRuleApplies) {
        // Freemium users: $0 tips trigger DND
        const zeroTipCount = confirmedDeliveries.filter(d => d.amounts?.tipAmount === 0).length;

        if (zeroTipCount >= 1) {
            return { dnd: true, reason: 'RULE_BASED' };
        }
    }

    return { dnd: false, reason: null };
}

export const updateAddressDeliveryStatsFlow = defineFlow(
  {
    name: 'updateAddressDeliveryStatsFlow',
    inputSchema: AddressStatsUpdateInputSchema,
    outputSchema: FlowOutputSchema,
  },
  async (input) => {
    const { userId, addressId } = input;
    const logPrefix = `[AddressStatsFlow ${userId}/${addressId}]`;

    try {
      // PHASE 1: Parallel data fetching (following successful pattern)
      const [addressDoc, deliveriesSnapshot, userDndPrefs] = await Promise.all([
        db.collection('users').doc(userId).collection('user_addresses').doc(addressId).get(),
        db.collection('users').doc(userId).collection('user_deliveries')
          .where('deliveryData.reference.addressId', '==', addressId)
          .limit(50)
          .get(),
        dndPreferencesCache.getUserDndPreferences(userId, logPrefix).catch(() => ({
          isPremiumUser: false,
          onboardingCompleted: false,
          customRule: { isEnabled: false, tipAmountThreshold: 0, comparisonType: 'less_than_or_equal_to' as const },
          defaultRuleApplies: true
        }))
      ]);

      if (!addressDoc.exists) {
        throw new Error(`Address ${addressId} not found`);
      }

      // PHASE 2: Heavy computation OUTSIDE transaction
      const deliveries = deliveriesSnapshot.docs.map(doc => ({
        id: doc.id,
        deliveryData: doc.data().deliveryData
      })) as Delivery[];

      const currentAddress = addressDoc.data();
      const manualDndState = currentAddress?.addressData?.flags?.manualDndState || null;

      // Calculate stats using simple aggregation
      const newStats = calculateAddressStats(deliveries);

      // Evaluate DND using existing architecture
      const dndResult = evaluateAddressDnd(deliveries, userDndPrefs, manualDndState);

      // PHASE 3: Minimal transaction with pre-computed data
      await db.runTransaction(async (transaction) => {
        const addressRef = db.collection('users').doc(userId).collection('user_addresses').doc(addressId);

        transaction.update(addressRef, {
          'addressData.deliveryStats': {
            deliveryCount: newStats.deliveryCount,
            tipCount: newStats.tipCount,
            totalTips: newStats.totalTips,
            pendingCount: newStats.pendingCount,
            averageTipAmount: newStats.averageTipAmount,
            highestTip: newStats.highestTip,
            lastDeliveryTimestamp: newStats.lastDeliveryTimestamp,
          },
          'addressData.orderIds': newStats.collectedOrderIds,
          'addressData.flags.doNotDeliver': dndResult.dnd,
          'addressData.flags.dndSource': dndResult.source,
          'addressData.flags.isVerified': dndResult.dnd,
          'addressData.metadata.updatedAt': FieldValue.serverTimestamp()
        });
      });

      console.log(`${logPrefix} SUCCESS: Updated stats and DND status`);
      return {
        addressId,
        status: "Success",
        updatedStats: {
          deliveryCount: newStats.deliveryCount,
          tipCount: newStats.tipCount,
          totalTips: newStats.totalTips,
          pendingCount: newStats.pendingCount,
          averageTipAmount: newStats.averageTipAmount,
          highestTip: newStats.highestTip,
          lastDeliveryTimestamp: newStats.lastDeliveryTimestamp,
        }
      };

    } catch (error: any) {
      console.error(`${logPrefix} ERROR:`, error.message);
      return { addressId, status: `Error: ${error.message}`, updatedStats: null };
    }
  }
);