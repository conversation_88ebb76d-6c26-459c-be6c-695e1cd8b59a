import * as z from 'zod';
import { defineFlow } from '@genkit-ai/flow';
import { getFirestore, Timestamp, FieldValue } from 'firebase-admin/firestore';
import { dndPreferencesCache, ComputedDndSettings } from '../utils/dnd-preferences-cache';

// ✅ PERFORMANCE: Initialize Firestore connection at module load to reduce cold start impact
const db = getFirestore();

// ✅ SIMPLIFIED IMPORTS: Use generated types directly
import type {
  Address,
  DeliveryStats,
  Flags
} from '../models/generated/address.schema';

import type {
  Delivery
} from '../models/generated/delivery.schema';

import type {
  Status
} from '../models/generated/status.schema';

// ✅ FIXED: Zod schemas that exactly match generated TypeScript types
const TimestampSchema = z.custom<Timestamp>((val) => {
  if (val === null || val === undefined) return true;
  if (val instanceof Timestamp) return true;
  // Handle complex timestamp objects from client
  if (val && typeof val === 'object' && typeof val.toDate === 'function') return true;
  return false;
}).nullable().optional();

// ✅ FIXED: DeliveryStats schema matching generated types exactly
const DeliveryStatsZodSchema = z.object({
  deliveryCount: z.number().nullable().optional(),
  tipCount: z.number().nullable().optional(),
  totalTips: z.number().nullable().optional(), // ✅ CRITICAL: Allow null for existing data
  highestTip: z.number().nullable().optional(),
  pendingCount: z.number().nullable().optional(),
  averageTimeMinutes: z.number().nullable().optional(),
  lastDeliveryDate: TimestampSchema,
  averageTipAmount: z.number().nullable().optional(),
  lastDeliveryTimestamp: TimestampSchema,
}).nullable().optional(); // ✅ Allow null for entire DeliveryStats object

// ✅ FIXED: Address validation schema matching generated types exactly
const AddressDataSchema = z.object({
  deliveryStats: DeliveryStatsZodSchema,
  orderIds: z.array(z.string()).nullable().optional(),
  flags: z.object({
    isFavorite: z.boolean().nullable().optional(),
    isVerified: z.boolean().nullable().optional(),
    doNotDeliver: z.boolean().nullable().optional(), // ✅ Match generated: nullable
    isApartment: z.boolean().nullable().optional(),
    isArchived: z.boolean().nullable().optional(),
    hasAccessIssues: z.boolean().nullable().optional(),
    manualDndState: z.enum(['FORCE_DND', 'FORCE_ALLOW']).nullable().optional(),
    dndSource: z.enum([
      'MANUAL_USER_FORCE_DND',
      'MANUAL_USER_FORCE_ALLOW', 
      'RULE_BASED_EXPLICIT_IMPORT',
      'RULE_BASED_USER_PREFERENCES'
    ]).nullable().optional(),
  }).nullable().optional(), // ✅ Match generated: Flags can be null
  metadata: z.object({
    createdAt: TimestampSchema,
    updatedAt: TimestampSchema,
    importedAt: TimestampSchema,
    source: z.string().nullable().optional(),
    importId: z.string().nullable().optional(),
    captureId: z.string().nullable().optional(),
    version: z.number().nullable().optional(),
    customData: z.record(z.any()).nullable().optional(), // ✅ Match generated: nullable
  }).nullable().optional(), // ✅ Match generated: Metadata can be null
}).passthrough(); // Allow additional fields at address data level

const AddressSchema = z.object({
  addressData: AddressDataSchema,
}).passthrough();

// ✅ FIXED: Delivery validation schema matching generated types exactly
const DeliveryDataSchema = z.object({
  userId: z.string(),
  orderId: z.string(),
  notes: z.string().nullable().optional(),
  reference: z.object({
    addressId: z.string().nullable().optional(),
    orderId: z.string().nullable().optional(),
    externalId: z.string().nullable().optional(),
    platformOrderId: z.string().nullable().optional(),
  }).nullable().optional(), // ✅ Match generated: Reference can be null
  amounts: z.object({
    basePay: z.number().nullable().optional(),
    tipAmount: z.number().nullable().optional(),
    tipPercentage: z.number().nullable().optional(),
    totalAmount: z.number().nullable().optional(),
    currencyCode: z.string().nullable().optional(),
    estimatedPay: z.number().nullable().optional(),
    finalPay: z.number().nullable().optional(),
    distanceMiles: z.number().nullable().optional(),
  }).nullable().optional(), // ✅ Match generated: Amounts can be null
  status: z.object({
    state: z.string().nullable().optional(),
    isTipped: z.boolean().nullable().optional(),
    isCompleted: z.boolean().nullable().optional(),
    isVerified: z.boolean().nullable().optional(),
    cancellationReason: z.string().nullable().optional(),
    verificationSource: z.string().nullable().optional(),
    verificationTimestamp: TimestampSchema,
    doNotDeliver: z.boolean().nullable().optional(),
    dndReason: z.enum(['EXPLICIT_IMPORT', 'RULE_BASED', 'MANUAL_TOGGLE']).nullable().optional(),
  }).nullable().optional(), // ✅ Match generated: Status can be null
  times: z.object({
    acceptedAt: TimestampSchema,
    completedAt: TimestampSchema,
    tippedAt: TimestampSchema,
  }).nullable().optional(), // ✅ Match generated: Times can be null
  metadata: z.object({
    createdAt: TimestampSchema,
    updatedAt: TimestampSchema,
    importedAt: TimestampSchema,
    source: z.string().nullable().optional(),
    importId: z.string().nullable().optional(),
    captureId: z.string().nullable().optional(),
    version: z.number().nullable().optional(),
    customData: z.record(z.any()).nullable().optional(), // ✅ Match generated: nullable
  }).nullable().optional(), // ✅ Match generated: Metadata can be null
}).passthrough();

const DeliverySchema = z.object({
  deliveryData: DeliveryDataSchema,
}).passthrough();

// ✅ FIXED: Flags validation schema matching generated types exactly
const FlagsSchema = z.object({
  isFavorite: z.boolean().nullable().optional(),
  isVerified: z.boolean().nullable().optional(),
  doNotDeliver: z.boolean().nullable().optional(), // ✅ Match generated: nullable
  isApartment: z.boolean().nullable().optional(),
  isArchived: z.boolean().nullable().optional(),
  hasAccessIssues: z.boolean().nullable().optional(),
  manualDndState: z.enum(['FORCE_DND', 'FORCE_ALLOW']).nullable().optional(),
  dndSource: z.enum([
    'MANUAL_USER_FORCE_DND',
    'MANUAL_USER_FORCE_ALLOW',
    'RULE_BASED_EXPLICIT_IMPORT', 
    'RULE_BASED_USER_PREFERENCES'
  ]).nullable().optional(),
}).nullable().optional(); // ✅ Match generated: entire Flags object can be null

// ✅ FIXED: Status validation schema matching generated types exactly
const StatusSchema = z.object({
  state: z.string().nullable().optional(),
  isTipped: z.boolean().nullable().optional(),
  isCompleted: z.boolean().nullable().optional(),
  isVerified: z.boolean().nullable().optional(),
  cancellationReason: z.string().nullable().optional(),
  verificationSource: z.string().nullable().optional(),
  verificationTimestamp: TimestampSchema,
  doNotDeliver: z.boolean().nullable().optional(),
  dndReason: z.enum(['EXPLICIT_IMPORT', 'RULE_BASED', 'MANUAL_TOGGLE']).nullable().optional(),
}).nullable().optional(); // ✅ Match generated: entire Status object can be null

// ✅ DEEP RUNTIME VALIDATION: Zod-based validation functions
// ✅ CRITICAL FIX: Preprocess address data to fix timestamps before validation
function preprocessAddressData(data: any): any {
  if (!data || typeof data !== 'object') return data;
  
  const processedData = { ...data };
  
  // Fix timestamps in address metadata
  if (processedData.addressData?.metadata) {
    if (processedData.addressData.metadata.createdAt) {
      const parsed = parseComplexTimestamp(processedData.addressData.metadata.createdAt);
      processedData.addressData.metadata.createdAt = parsed ? Timestamp.fromDate(parsed) : null;
    }
    if (processedData.addressData.metadata.updatedAt) {
      const parsed = parseComplexTimestamp(processedData.addressData.metadata.updatedAt);
      processedData.addressData.metadata.updatedAt = parsed ? Timestamp.fromDate(parsed) : null;
    }
    if (processedData.addressData.metadata.importedAt) {
      const parsed = parseComplexTimestamp(processedData.addressData.metadata.importedAt);
      processedData.addressData.metadata.importedAt = parsed ? Timestamp.fromDate(parsed) : null;
    }
  }
  
  // Fix timestamps in delivery stats
  if (processedData.addressData?.deliveryStats) {
    if (processedData.addressData.deliveryStats.lastDeliveryDate) {
      const parsed = parseComplexTimestamp(processedData.addressData.deliveryStats.lastDeliveryDate);
      processedData.addressData.deliveryStats.lastDeliveryDate = parsed ? Timestamp.fromDate(parsed) : null;
    }
    if (processedData.addressData.deliveryStats.lastDeliveryTimestamp) {
      const parsed = parseComplexTimestamp(processedData.addressData.deliveryStats.lastDeliveryTimestamp);
      processedData.addressData.deliveryStats.lastDeliveryTimestamp = parsed ? Timestamp.fromDate(parsed) : null;
    }
  }
  
  return processedData;
}

export function validateAndCastAddress(data: any): Address | null {
  // ✅ PREPROCESS: Fix timestamps before validation
  const preprocessedData = preprocessAddressData(data);
  
  const result = AddressSchema.safeParse(preprocessedData);
  if (!result.success) {
    console.error('❌ VALIDATION ERROR: Address validation failed for data:', JSON.stringify(data, null, 2));
    console.error('❌ VALIDATION DETAILS:', JSON.stringify(result.error.format(), null, 2));
    console.error('❌ VALIDATION ISSUES:', result.error.issues.map(issue => `${issue.path.join('.')} - ${issue.message}`));
    return null;
  }
  return result.data as Address;
}

// ✅ CRITICAL FIX: Preprocess data to fix timestamps before validation
function preprocessDeliveryData(data: any): any {
  if (!data || typeof data !== 'object') return data;
  
  const processedData = { ...data };
  
  // Fix timestamps in times object
  if (processedData.times) {
    if (processedData.times.completedAt) {
      const parsed = parseComplexTimestamp(processedData.times.completedAt);
      processedData.times.completedAt = parsed ? Timestamp.fromDate(parsed) : null;
    }
    if (processedData.times.acceptedAt) {
      const parsed = parseComplexTimestamp(processedData.times.acceptedAt);
      processedData.times.acceptedAt = parsed ? Timestamp.fromDate(parsed) : null;
    }
    if (processedData.times.tippedAt) {
      const parsed = parseComplexTimestamp(processedData.times.tippedAt);
      processedData.times.tippedAt = parsed ? Timestamp.fromDate(parsed) : null;
    }
  }
  
  // Fix timestamps in metadata object
  if (processedData.metadata) {
    if (processedData.metadata.createdAt) {
      const parsed = parseComplexTimestamp(processedData.metadata.createdAt);
      processedData.metadata.createdAt = parsed ? Timestamp.fromDate(parsed) : null;
    }
    if (processedData.metadata.updatedAt) {
      const parsed = parseComplexTimestamp(processedData.metadata.updatedAt);
      processedData.metadata.updatedAt = parsed ? Timestamp.fromDate(parsed) : null;
    }
    if (processedData.metadata.importedAt) {
      const parsed = parseComplexTimestamp(processedData.metadata.importedAt);
      processedData.metadata.importedAt = parsed ? Timestamp.fromDate(parsed) : null;
    }
  }
  
  return processedData;
}

export function validateAndCastDelivery(data: any, docId?: string): Delivery | null {
  // ✅ PREPROCESS: Fix timestamps before validation
  const preprocessedData = {
    deliveryData: preprocessDeliveryData(data.deliveryData || data)
  };
  
  const result = DeliverySchema.safeParse(preprocessedData);
  if (!result.success) {
    console.error('❌ DELIVERY VALIDATION ERROR:', JSON.stringify(result.error.format(), null, 2));
    console.error('❌ DELIVERY VALIDATION ISSUES:', result.error.issues.map(issue => `${issue.path.join('.')} - ${issue.message}`));
    return null;
  }
  // ✅ SAFE CASTING: Zod validation ensures type safety, add document ID
  return {
    ...result.data,
    id: docId || result.data.id || ''
  } as Delivery;
}

export function validateAndCastFlags(data: any): Flags | null {
  const result = FlagsSchema.safeParse(data);
  if (!result.success) {
    console.error('❌ FLAGS VALIDATION ERROR:', JSON.stringify(result.error.format(), null, 2));
    console.error('❌ FLAGS VALIDATION ISSUES:', result.error.issues.map(issue => `${issue.path.join('.')} - ${issue.message}`));
    return null;
  }
  return result.data as Flags;
}

// ✅ FIXED: Data sanitization that ensures proper defaults instead of null values
export function sanitizeAddressData(rawData: any): Partial<Address['addressData']> {
  return {
    deliveryStats: (rawData?.deliveryStats && typeof rawData.deliveryStats === 'object') ? {
      deliveryCount: typeof rawData.deliveryStats.deliveryCount === 'number' ? rawData.deliveryStats.deliveryCount : 0,
      tipCount: typeof rawData.deliveryStats.tipCount === 'number' ? rawData.deliveryStats.tipCount : 0,
      totalTips: typeof rawData.deliveryStats.totalTips === 'number' ? rawData.deliveryStats.totalTips : 0, // ✅ Always number, never null
      highestTip: typeof rawData.deliveryStats.highestTip === 'number' ? rawData.deliveryStats.highestTip : null,
      pendingCount: typeof rawData.deliveryStats.pendingCount === 'number' ? rawData.deliveryStats.pendingCount : 0,
      averageTimeMinutes: typeof rawData.deliveryStats.averageTimeMinutes === 'number' ? rawData.deliveryStats.averageTimeMinutes : null,
      lastDeliveryDate: rawData.deliveryStats.lastDeliveryDate instanceof Timestamp ? rawData.deliveryStats.lastDeliveryDate : null,
      averageTipAmount: typeof rawData.deliveryStats.averageTipAmount === 'number' ? rawData.deliveryStats.averageTipAmount : null,
      lastDeliveryTimestamp: rawData.deliveryStats.lastDeliveryTimestamp instanceof Timestamp ? rawData.deliveryStats.lastDeliveryTimestamp : null,
    } : {
      // ✅ PROPER DEFAULTS: Initialize with proper defaults instead of null
      deliveryCount: 0,
      tipCount: 0,
      totalTips: 0, // ✅ Never null as per generated schema
      highestTip: null,
      pendingCount: 0,
      averageTimeMinutes: null,
      lastDeliveryDate: null,
      averageTipAmount: null,
      lastDeliveryTimestamp: null,
    },
    orderIds: Array.isArray(rawData?.orderIds) ? rawData.orderIds.filter((id: any) => typeof id === 'string') : [],
    flags: (rawData?.flags && typeof rawData.flags === 'object') ? {
      isFavorite: typeof rawData.flags.isFavorite === 'boolean' ? rawData.flags.isFavorite : null,
      isVerified: typeof rawData.flags.isVerified === 'boolean' ? rawData.flags.isVerified : null,
      doNotDeliver: typeof rawData.flags.doNotDeliver === 'boolean' ? rawData.flags.doNotDeliver : null, // ✅ Allow null as per schema
      isApartment: typeof rawData.flags.isApartment === 'boolean' ? rawData.flags.isApartment : null,
      isArchived: typeof rawData.flags.isArchived === 'boolean' ? rawData.flags.isArchived : null,
      hasAccessIssues: typeof rawData.flags.hasAccessIssues === 'boolean' ? rawData.flags.hasAccessIssues : null,
      manualDndState: ['FORCE_DND', 'FORCE_ALLOW'].includes(rawData.flags.manualDndState) ? rawData.flags.manualDndState : null,
      dndSource: typeof rawData.flags.dndSource === 'string' || rawData.flags.dndSource === null ? rawData.flags.dndSource : null,
    } : {
      // ✅ PROPER DEFAULTS: Use null for optional fields as per generated schema
      isFavorite: null,
      isVerified: null,
      doNotDeliver: null,
      isApartment: null,
      isArchived: null,
      hasAccessIssues: null,
      manualDndState: null,
      dndSource: null,
    },
    metadata: (rawData?.metadata && typeof rawData.metadata === 'object') ? {
      createdAt: rawData.metadata.createdAt instanceof Timestamp ? rawData.metadata.createdAt : null,
      updatedAt: rawData.metadata.updatedAt instanceof Timestamp ? rawData.metadata.updatedAt : null,
      importedAt: rawData.metadata.importedAt instanceof Timestamp ? rawData.metadata.importedAt : null,
      source: typeof rawData.metadata.source === 'string' ? rawData.metadata.source : null,
      importId: typeof rawData.metadata.importId === 'string' ? rawData.metadata.importId : null,
      captureId: typeof rawData.metadata.captureId === 'string' ? rawData.metadata.captureId : null,
      version: typeof rawData.metadata.version === 'number' ? rawData.metadata.version : null,
      customData: rawData.metadata.customData && typeof rawData.metadata.customData === 'object' ? {
        operationLog: Array.isArray(rawData.metadata.customData.operationLog) ? 
          rawData.metadata.customData.operationLog.filter((op: any) => typeof op === 'number') : [],
        ...rawData.metadata.customData
      } : { operationLog: [] }, // ✅ Provide object default, not null
    } : {
      // ✅ PROPER DEFAULTS: Initialize with defaults
      createdAt: null,
      updatedAt: null,
      importedAt: null,
      source: null,
      importId: null,
      captureId: null,
      version: null,
      customData: { operationLog: [] }, // ✅ Object default, not null
    }
  };
}

export function validateAndCastStatus(data: any): Status | null {
  const result = StatusSchema.safeParse(data);
  if (!result.success) {
    console.error('❌ STATUS VALIDATION ERROR:', JSON.stringify(result.error.format(), null, 2));
    console.error('❌ STATUS VALIDATION ISSUES:', result.error.issues.map(issue => `${issue.path.join('.')} - ${issue.message}`));
    return null;
  }
  return result.data as Status;
}




const FlowOutputSchema = z.object({
  addressId: z.string().nullable(),
  status: z.string(),
  updatedStats: DeliveryStatsZodSchema,
});

// ✅ ENHANCED CIRCUIT BREAKER: Per-address tracking with operation type awareness and sliding window
const CIRCUIT_BREAKER_CONFIG = {
  // Operation-specific thresholds (milliseconds)
  thresholds: {
    'delivery_create': 300,      // New delivery creation
    'tip_update': 200,           // Tip amount/status changes
    'address_change': 500,       // Address changes for existing delivery
    'manual_override': 100,      // Manual DND toggles
    'bulk_import': 1000,         // Bulk import operations
    'default': 500               // Fallback for unknown operations
  },
  // Sliding window rate limiting
  slidingWindow: {
    windowMs: 30000,             // 30 second window
    maxOperationsPerWindow: 15,  // Max operations per address per window
    maxGlobalPerWindow: 50       // Max operations per user across all addresses
  }
};

// ✅ OPERATION TYPE DETECTION: Used by delivery triggers to optimize circuit breaker thresholds
// This function is used by delivery document triggers to detect the type of operation
// and apply appropriate circuit breaker thresholds when calling the stats updater
export function detectOperationType(
  beforeData: Delivery | null,
  afterData: Delivery,
  isNewDocument: boolean
): keyof typeof CIRCUIT_BREAKER_CONFIG.thresholds {
  if (isNewDocument) {
    return 'delivery_create';
  }

  const beforeTip = beforeData?.deliveryData?.amounts?.tipAmount;
  const afterTip = afterData?.deliveryData?.amounts?.tipAmount;
  const beforeAddress = beforeData?.deliveryData?.reference?.addressId;
  const afterAddress = afterData?.deliveryData?.reference?.addressId;

  // Address change detection
  if (beforeAddress !== afterAddress) {
    return 'address_change';
  }

  // Tip update detection (including pending state changes)
  if (beforeTip !== afterTip) {
    return 'tip_update';
  }

  return 'default';
}

// ✅ PHASE 3: SAFETY - In-memory circuit breaker to prevent runaway loops
const addressUpdateTracker = new Map<string, number[]>();

// Cleanup old entries from the tracker to prevent memory leaks
setInterval(() => {
  const now = Date.now();
  const windowMs = CIRCUIT_BREAKER_CONFIG.slidingWindow.windowMs;
  let cleanedCount = 0;
  for (const [addressId, timestamps] of addressUpdateTracker.entries()) {
    const recentTimestamps = timestamps.filter(ts => now - ts < windowMs * 2); // Keep for 2 windows
    if (recentTimestamps.length > 0) {
      addressUpdateTracker.set(addressId, recentTimestamps);
    } else {
      addressUpdateTracker.delete(addressId);
      cleanedCount++;
    }
  }
  if (cleanedCount > 0) {
    console.log(`[CircuitBreaker] Cleaned up ${cleanedCount} old address trackers.`);
  }
}, 300_000); // Cleanup every 5 minutes

async function checkForPotentialLoop(
  userId: string,
  addressId: string,
  operationType: string,
  logPrefix: string
): Promise<boolean> {
  const now = Date.now();
  const { windowMs, maxOperationsPerWindow } = CIRCUIT_BREAKER_CONFIG.slidingWindow;

  const timestamps = addressUpdateTracker.get(addressId) || [];
  
  // Filter timestamps to the current sliding window
  const recentTimestamps = timestamps.filter(ts => now - ts < windowMs);

  if (recentTimestamps.length >= maxOperationsPerWindow) {
    console.warn(`${logPrefix} CIRCUIT BREAKER TRIPPED for address ${addressId}. Found ${recentTimestamps.length} updates in the last ${windowMs / 1000}s.`);
    // Optionally, log the timestamps for debugging
    console.warn(`${logPrefix} Timestamps: ${JSON.stringify(recentTimestamps)}`);
    return true; // Trip the breaker
  }

  // Add current operation timestamp and update tracker
  recentTimestamps.push(now);
  addressUpdateTracker.set(addressId, recentTimestamps);

  console.log(`${logPrefix} Circuit breaker check passed for address ${addressId}. Operations in window: ${recentTimestamps.length}/${maxOperationsPerWindow}.`);
  return false;
}

// Helper function to convert complex OffsetDateTime structure to JavaScript Date
export function parseComplexTimestamp(timestampInput: any): Date | null {
  if (!timestampInput) return null;

  // Handle Firestore Timestamp
  if (timestampInput instanceof Timestamp) {
    return timestampInput.toDate();
  }

  // Handle objects with toDate() method
  if (timestampInput && typeof timestampInput.toDate === 'function') {
    return timestampInput.toDate();
  }

  // Handle complex OffsetDateTime structure from app
  if (timestampInput && typeof timestampInput === 'object' &&
      typeof timestampInput.year === 'number' &&
      typeof timestampInput.monthValue === 'number' &&
      typeof timestampInput.dayOfMonth === 'number') {

    try {
      // Convert OffsetDateTime structure to JavaScript Date
      // Note: JavaScript months are 0-based, but OffsetDateTime monthValue is 1-based
      const jsDate = new Date(
        timestampInput.year,
        timestampInput.monthValue - 1, // Convert to 0-based month
        timestampInput.dayOfMonth,
        timestampInput.hour || 0,
        timestampInput.minute || 0,
        timestampInput.second || 0,
        Math.floor((timestampInput.nano || 0) / 1000000) // Convert nanoseconds to milliseconds
      );

      // Apply timezone offset if present
      if (timestampInput.offset && typeof timestampInput.offset.totalSeconds === 'number') {
        const offsetMillis = timestampInput.offset.totalSeconds * 1000;
        jsDate.setTime(jsDate.getTime() - offsetMillis); // Adjust for timezone
      }

      return jsDate;
    } catch (error) {
      console.warn('Failed to parse complex timestamp:', timestampInput, error);
      return null;
    }
  }

  console.warn('Unknown timestamp format:', timestampInput);
  return null;
}

export const AddressStatsUpdateInputSchema = z.object({
  userId: z.string(),
  addressId: z.string(),
  deliveryId: z.string().optional(), // ✅ CRITICAL FIX: Add deliveryId to target specific delivery for updates
});

// ✅ CRITICAL FIX: Move heavy computation outside transaction
async function processDeliveriesData(
  deliveriesSnapshot: FirebaseFirestore.QuerySnapshot,
  addressId: string,
  userDndPrefs: ComputedDndSettings,
  logPrefix: string,
  existingOrderIdsFromDisk: string[],
  manualDndState: "FORCE_DND" | "FORCE_ALLOW" | null,
  hasIncompleteHistory = false // ✅ Added incomplete history flag
): Promise<{
  deliveryCount: number;
  tipCount: number;
  pendingCount: number;
  totalTips: number;
  highestTip: number | null;
  lastDeliveryTimestamp: Timestamp | null;
  calculatedAverageTime: number | null;
  calculatedAverageTip: number;
  finalAddressDndStatus: boolean;
  finalAddressVerifiedStatus: boolean;
  finalAddressDndSource: "MANUAL_USER_FORCE_DND" | "MANUAL_USER_FORCE_ALLOW" | "RULE_BASED_EXPLICIT_IMPORT" | "RULE_BASED_USER_PREFERENCES" | null;
  collectedOrderIds: Set<string>;
  targetDeliveryForUpdate: { id: string; data: Delivery } | null;
  manualDndState: "FORCE_DND" | "FORCE_ALLOW" | null;
}> {
  // Initialize variables
  let deliveryCount = 0;
  let tipCount = 0;
  let pendingCount = 0;
  let totalTips = 0; // Always initialize as number, never undefined
  let highestTip: number | null = null;
  let lastDeliveryTimestamp: Timestamp | null = null;
  let totalDeliveryTimeMinutes = 0;
  let deliveriesWithTime = 0;
  const collectedOrderIds = new Set<string>(existingOrderIdsFromDisk);

  // DND evaluation variables
  let finalAddressDndStatus = false;
  let finalAddressVerifiedStatus = false;
  let finalAddressDndSource: "MANUAL_USER_FORCE_DND" | "MANUAL_USER_FORCE_ALLOW" | "RULE_BASED_EXPLICIT_IMPORT" | "RULE_BASED_USER_PREFERENCES" | null = null;
  let targetDeliveryForUpdate: { id: string; data: Delivery } | null = null;
  let mostRecentDeliveryTimestamp = 0;

  // ✅ SINGLE-PASS PROCESSING: Combine DND evaluation and stats calculation
  const allDeliveryDataObjects: Delivery['deliveryData'][] = [];
  const invalidDeliveries: string[] = [];

  deliveriesSnapshot.forEach(doc => {
    // ✅ RUNTIME VALIDATION: Safe casting with validation and document ID
    const fullDocData = validateAndCastDelivery(doc.data(), doc.id);
    if (!fullDocData) {
      console.warn(`${logPrefix} Invalid delivery data structure for document ${doc.id}. Skipping.`);
      invalidDeliveries.push(doc.id);
      return;
    }

    const deliveryData = fullDocData.deliveryData;

    // ✅ VALIDATION: Verify this delivery actually belongs to this address
    if (deliveryData.reference?.addressId !== addressId) {
      console.warn(`${logPrefix} Delivery ${doc.id} has mismatched addressId: expected ${addressId}, got ${deliveryData.reference?.addressId}. Skipping.`);
      invalidDeliveries.push(doc.id);
      return;
    }

    // Track for DND evaluation
    allDeliveryDataObjects.push(deliveryData);

    // Find most recent delivery for potential update
    const completedAt = deliveryData.times?.completedAt;
    const createdAt = deliveryData.metadata?.createdAt;
    const timestampToUse = completedAt || createdAt;

    if (timestampToUse && timestampToUse instanceof Timestamp) {
      const timestampMs = timestampToUse.toMillis();
      if (timestampMs > mostRecentDeliveryTimestamp) {
        mostRecentDeliveryTimestamp = timestampMs;
        targetDeliveryForUpdate = { id: doc.id, data: fullDocData };
      }
    }

    // ✅ STATS CALCULATION: Process delivery for statistics
    deliveryCount++;
    collectedOrderIds.add(deliveryData.orderId);

    // ✅ FIXED TIP STATE ANALYSIS: Better logic for detecting tip status
    const tipAmount = deliveryData.amounts?.tipAmount ?? null;
    const deliveryStatus = validateAndCastStatus(deliveryData.status);
    const isTipped = deliveryStatus?.isTipped ?? false;
    const isCompleted = deliveryStatus?.isCompleted ?? false;

    // ✅ CORRECTED LOGIC: Distinguish between "pending $0" vs "received $0"
    // A delivery is only "received" if BOTH conditions are met:
    // 1. tipAmount is set (could be 0 or any number)
    // 2. Delivery is marked as completed/tipped (not pending)
    
    if (tipAmount != null && typeof tipAmount === 'number' && (isTipped === true || isCompleted === true)) {
      // Count as received tip: explicit amount AND confirmed status
      tipCount++;
      totalTips += tipAmount;
      if (highestTip === null || tipAmount > highestTip) {
        highestTip = tipAmount;
      }
    } else {
      // Count as pending: either no tip amount set OR status is still pending
      pendingCount++;
    }

    // Track last delivery timestamp (for both pending and confirmed)
    if (timestampToUse && timestampToUse instanceof Timestamp) {
      if (lastDeliveryTimestamp === null || timestampToUse.toMillis() > lastDeliveryTimestamp.toMillis()) {
        lastDeliveryTimestamp = timestampToUse;
      }
    }

    // Calculate delivery time if both accepted and completed timestamps are available
    const acceptedAt = deliveryData.times?.acceptedAt;
    if (acceptedAt && completedAt &&
        acceptedAt instanceof Timestamp && completedAt instanceof Timestamp) {
      const deliveryTimeMs = completedAt.toMillis() - acceptedAt.toMillis();
      if (deliveryTimeMs > 0) {
        totalDeliveryTimeMinutes += deliveryTimeMs / (1000 * 60);
        deliveriesWithTime++;
      }
    }
  });

  if (invalidDeliveries.length > 0) {
    console.warn(`${logPrefix} Found ${invalidDeliveries.length} invalid deliveries: ${invalidDeliveries.join(', ')}`);
  }

  // ✅ DND EVALUATION: Process based on manual state or most recent delivery
  if (manualDndState === 'FORCE_DND') {
    console.log(`${logPrefix} FAST-PATH: Manual override FORCE_DND detected`);
    finalAddressDndStatus = true;
    finalAddressVerifiedStatus = true;
    finalAddressDndSource = 'MANUAL_USER_FORCE_DND';
  } else if (manualDndState === 'FORCE_ALLOW') {
    console.log(`${logPrefix} FAST-PATH: Manual override FORCE_ALLOW detected`);
    finalAddressDndStatus = false;
    finalAddressVerifiedStatus = true;
    finalAddressDndSource = 'MANUAL_USER_FORCE_ALLOW';
  } else if (allDeliveryDataObjects.length > 0) {
    // ✅ ENHANCED DND EVALUATION: Evaluate patterns across multiple recent deliveries
    const sortedDeliveries = allDeliveryDataObjects.sort((a, b) => {
      const aCompletedAt = parseComplexTimestamp(a.times?.completedAt)?.getTime() ??
                         parseComplexTimestamp(a.metadata?.importedAt)?.getTime() ??
                         parseComplexTimestamp(a.metadata?.createdAt)?.getTime() ?? 0;
      const bCompletedAt = parseComplexTimestamp(b.times?.completedAt)?.getTime() ??
                         parseComplexTimestamp(b.metadata?.importedAt)?.getTime() ??
                         parseComplexTimestamp(b.metadata?.createdAt)?.getTime() ?? 0;
      return bCompletedAt - aCompletedAt;
    });

    // ✅ PROPER INTEGRATION: Use existing function with multiple deliveries and incomplete history flag
    const dndResult = evaluateDndForDeliveryData(sortedDeliveries, userDndPrefs, hasIncompleteHistory);
    finalAddressDndStatus = dndResult.dnd;
    finalAddressVerifiedStatus = dndResult.dnd;
    if (dndResult.dnd) {
      if (dndResult.reason === 'EXPLICIT_IMPORT') {
        finalAddressDndSource = 'RULE_BASED_EXPLICIT_IMPORT';
      } else {
        finalAddressDndSource = 'RULE_BASED_USER_PREFERENCES';
      }
    } else {
      finalAddressDndSource = null;
    }
    
    console.log(`${logPrefix} 🔍 DND EVALUATION: Analyzed ${sortedDeliveries.length} deliveries, result: ${dndResult.dnd ? 'DND' : 'ALLOW'} (${dndResult.reason || 'no rule triggered'})`);
  } else {
    console.log(`${logPrefix} 🔍 DND EVALUATION: No valid deliveries found for address ${addressId}`);
    finalAddressDndStatus = false;
    finalAddressVerifiedStatus = false;
    finalAddressDndSource = null;
  }

  // Calculate averages
  const calculatedAverageTip = tipCount > 0 ? totalTips / tipCount : 0;
  const calculatedAverageTime = deliveriesWithTime > 0 ? totalDeliveryTimeMinutes / deliveriesWithTime : null;

  // ✅ STRUCTURED LOGGING: Summary instead of per-delivery logs
  console.log(`${logPrefix} PROCESSING SUMMARY:`, {
    deliveryCount,
    tipCount,
    pendingCount,
    totalTips,
    averageTip: calculatedAverageTip,
    averageTimeMinutes: calculatedAverageTime,
    highestTip,
    finalDndStatus: finalAddressDndStatus,
    dndSource: finalAddressDndSource,
    manualOverride: manualDndState || 'none',
    invalidDeliveries: invalidDeliveries.length,
    targetDeliveryId: (targetDeliveryForUpdate as { id: string; data: Delivery } | null)?.id || 'none'
  });

  return {
    deliveryCount,
    tipCount,
    pendingCount,
    totalTips,
    highestTip,
    lastDeliveryTimestamp,
    calculatedAverageTime,
    calculatedAverageTip,
    finalAddressDndStatus,
    finalAddressVerifiedStatus,
    finalAddressDndSource,
    collectedOrderIds,
    targetDeliveryForUpdate,
    manualDndState
  };
}

// ✅ ENHANCED: Multi-delivery DND evaluation using existing architecture integration
export function evaluateDndForDeliveryData(
    allDeliveryData: Delivery['deliveryData'][], // ✅ Now accepts multiple deliveries
    userDndPrefs: ComputedDndSettings,
    hasIncompleteHistory = false // ✅ Flag to indicate if we hit query limits
): { dnd: boolean; reason: "EXPLICIT_IMPORT" | "RULE_BASED" | null } {
    if (allDeliveryData.length === 0) {
        return { dnd: false, reason: null };
    }

    // 1. Check for explicit import DND flag (highest priority) - any delivery with this flag triggers DND
    const hasExplicitImportDnd = allDeliveryData.some(deliveryData => {
        const status = validateAndCastStatus(deliveryData.status);
        return status?.dndReason === 'EXPLICIT_IMPORT';
    });
    
    if (hasExplicitImportDnd) {
        return { dnd: true, reason: 'EXPLICIT_IMPORT' };
    }

    // 2. If we have incomplete history due to query limits, bias toward NOT applying DND
    if (hasIncompleteHistory && allDeliveryData.length >= 500) {
        console.log('DND evaluation: Incomplete delivery history detected, requiring stronger evidence for DND');
    }

    // 3. Analyze recent deliveries (last 5 or deliveries from last 30 days, whichever is more)
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    const recentDeliveries = allDeliveryData
        .filter(deliveryData => {
            const completedAt = parseComplexTimestamp(deliveryData.times?.completedAt) || 
                              parseComplexTimestamp(deliveryData.metadata?.createdAt);
            return completedAt ? completedAt.getTime() >= thirtyDaysAgo : true; // Include if no timestamp
        })
        .slice(0, 5); // Limit to 5 most recent

    // 4. Analyze confirmed deliveries (exclude pending)
    const confirmedDeliveries = recentDeliveries.filter(deliveryData => {
        const tipAmount = deliveryData.amounts?.tipAmount ?? null;
        const status = validateAndCastStatus(deliveryData.status);
        const isTipped = status?.isTipped ?? false;
        const isCompleted = status?.isCompleted ?? false;
        
        // ✅ CORRECTED: A delivery is confirmed only if BOTH tipAmount is set AND status is completed/tipped
        // This properly distinguishes "pending $0" from "received $0"
        const isPending = tipAmount == null || !(isTipped === true || isCompleted === true);
        return !isPending;
    });

    if (confirmedDeliveries.length === 0) {
        // All recent deliveries are pending - don't apply DND rules yet
        return { dnd: false, reason: null };
    }

    // 5. Apply DND rules based on confirmed delivery patterns (more conservative thresholds)
    if (userDndPrefs.isPremiumUser && userDndPrefs.customRule?.isEnabled) {
        // Premium users: Custom threshold rule - require at least 3 confirmed deliveries AND 2+ below threshold
        const threshold = userDndPrefs.customRule.tipAmountThreshold;
        const lowTipDeliveries = confirmedDeliveries.filter(deliveryData => {
            const tipAmount = deliveryData.amounts?.tipAmount ?? 0;
            return tipAmount <= threshold;
        });
        
        // ✅ CONSERVATIVE: Require substantial evidence before DND (at least 3 deliveries, 2+ poor)
        // ✅ FAIRNESS: If history is incomplete, require even stronger evidence
        const minDeliveries = hasIncompleteHistory ? 4 : 3;
        const minPoorDeliveries = hasIncompleteHistory ? 3 : 2;
        
        if (confirmedDeliveries.length >= minDeliveries && lowTipDeliveries.length >= minPoorDeliveries) {
            return { dnd: true, reason: 'RULE_BASED' };
        }
    } else if (userDndPrefs.defaultRuleApplies) {
        // Freemium users: Default rule - confirmed $0 tips trigger DND
        const zeroTipDeliveries = confirmedDeliveries.filter(deliveryData => {
            const tipAmount = deliveryData.amounts?.tipAmount ?? null;
            
            // ✅ CORRECTED: Only received $0 tips trigger DND (not pending $0s)
            // Since we're filtering from confirmedDeliveries, these are already confirmed
            return tipAmount === 0;
        });
        
        // ✅ CONSERVATIVE: Only trigger DND if recent pattern shows confirmed zero tips
        // ✅ FAIRNESS: If history is incomplete, require even more evidence
        const minConfirmedDeliveries = hasIncompleteHistory ? 3 : 2;
        
        if (zeroTipDeliveries.length >= 1 && confirmedDeliveries.length >= minConfirmedDeliveries) {
            return { dnd: true, reason: 'RULE_BASED' };
        }
    }

    // Default: Allow delivery (positive tips, insufficient evidence, or pending)
    return { dnd: false, reason: null };
}

export const updateAddressDeliveryStatsFlow = defineFlow(
  {
    name: 'updateAddressDeliveryStatsFlow',
    inputSchema: AddressStatsUpdateInputSchema,
    outputSchema: FlowOutputSchema,
  },
  async (input) => {
    const { userId, addressId, deliveryId } = input;
    const logPrefix = `[UpdateAddressStatsFlow UserId: ${userId}, AddressId: ${addressId}${deliveryId ? `, DeliveryId: ${deliveryId}` : ''}] -`;
    console.log(`${logPrefix} Starting address delivery stats recalculation.`);

    if (!userId || !addressId) {
      console.error(`${logPrefix} Critical: userId or addressId is missing. Aborting. Input:`, input);
      return { addressId: addressId || null, status: "Error: Missing userId or addressId", updatedStats: null };
    }

    // ✅ OPERATION TYPE INTEGRATION: Detect operation type for circuit breaker optimization
    // For stats updater, we default to 'default' but could be enhanced with context
    const operationType = 'default'; // Stats updater is triggered by various delivery operations
    const isLoopDetected = await checkForPotentialLoop(userId, addressId, operationType, logPrefix);
    if (isLoopDetected) {
      console.warn(`${logPrefix} CIRCUIT BREAKER ACTIVATED: Backing off to prevent potential loop.`);
      return { addressId, status: "Throttled - Circuit breaker activated", updatedStats: null };
    }

    try {
      // ✅ PERFORMANCE FIX: Simplified DND preferences with fallback
      let userDndPrefs: ComputedDndSettings;
      try {
        console.log(`${logPrefix} PHASE 0: Fetching DND preferences`);
        userDndPrefs = await dndPreferencesCache.getUserDndPreferences(userId, logPrefix);
        console.log(`${logPrefix} PHASE 0: DND preferences fetched successfully`);
      } catch (dndError) {
        console.warn(`${logPrefix} DND preferences fetch failed, using fallback:`, dndError);
        // ✅ FALLBACK: Use default preferences to avoid blocking
        userDndPrefs = {
          isPremiumUser: false,
          onboardingCompleted: false,
          customRule: { isEnabled: false, tipAmountThreshold: 0, comparisonType: 'less_than_or_equal_to' as const },
          defaultRuleApplies: true
        };
      }
      // ✅ PERFORMANCE FIX: Optimized timeouts for Cloud Functions cold starts
      const addressRef = db.collection('users').doc(userId).collection('user_addresses').doc(addressId);
      
      console.log(`${logPrefix} PHASE 1: Starting parallel data reads with cloud-optimized timeouts`);

      // ✅ PHASE 1.2: PARALLELIZE READS - Fetch address and deliveries concurrently with retry logic
      // ✅ CLOUD FUNCTIONS OPTIMIZATION: Longer timeouts for cold start compatibility
      const addressPromise = Promise.race([
        addressRef.get(),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Address read timeout')), 25000))
      ]);
      
      // ✅ QUERY OPTIMIZATION: Simplified query for better index compatibility
      const deliveriesQuery = db.collection('users').doc(userId).collection('user_deliveries')
        .where('deliveryData.reference.addressId', '==', addressId)
        .orderBy('deliveryData.metadata.createdAt', 'desc')
        .limit(50); // ✅ FURTHER REDUCED LIMIT: Even smaller for cold start performance
        
      const deliveriesPromise = Promise.race([
        deliveriesQuery.get(),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Deliveries read timeout')), 30000))
      ]);

      // ✅ DEFENSIVE: Add timing information for debugging connection issues
      const startTime = Date.now();
      const [addressDoc, deliveriesSnapshot] = await Promise.all([addressPromise, deliveriesPromise]) as [any, any];
      const readDuration = Date.now() - startTime;
      
      console.log(`${logPrefix} PHASE 1: Firestore reads completed in ${readDuration}ms`);

      console.log(`${logPrefix} PHASE 1: Parallel reads completed successfully, found ${deliveriesSnapshot.size} deliveries`);
      
      if (!addressDoc.exists) {
        throw new Error(`Address ${addressId} not found for user ${userId}`);
      }

      // ✅ MONITORING: Track if we hit the query limit (may need pagination)
      const hasIncompleteHistory = deliveriesSnapshot.size >= 50; // ✅ Updated for cold start optimization
      if (hasIncompleteHistory) {
        console.warn(`${logPrefix} Query limit reached: Found 50+ deliveries for address ${addressId}. Consider implementing pagination.`);
        console.warn(`${logPrefix} DND evaluation may be incomplete due to limited delivery history.`);
      }

      // ✅ CRITICAL VALIDATION: Check if we have any deliveries to process
      if (deliveriesSnapshot.empty) {
        console.warn(`${logPrefix} No deliveries found for address ${addressId}. This could indicate:`);
        console.warn(`${logPrefix} - Address was changed for all deliveries (moved to different address)`);
        console.warn(`${logPrefix} - All deliveries were deleted`);
        console.warn(`${logPrefix} - Address stats may be stale and need cleanup`);
        // Continue processing to update address stats (will result in zero counts)
      }

      // ✅ REFERENCES: Define user profile reference for transaction
      const userProfileRef = db.collection('users').doc(userId);

      console.log(`${logPrefix} PHASE 2: Processing delivery data outside transaction`);

      // ✅ RUNTIME VALIDATION: Safe casting with graceful degradation
      let currentFlags: Flags | null;
      let manualDndState: "FORCE_DND" | "FORCE_ALLOW" | null;
      let existingOrderIdsFromDisk: string[];
      let previousAddressStats: any;
      
      console.log(`${logPrefix} PHASE 2A: Validating address data`);
      const addressData = validateAndCastAddress(addressDoc.data());
      if (!addressData) {
        console.warn(`${logPrefix} FALLBACK: Address validation failed, using simplified fallback`);
        
        // ✅ SIMPLIFIED FALLBACK: Use minimal defaults
        currentFlags = null;
        manualDndState = null;
        existingOrderIdsFromDisk = [];
        previousAddressStats = { deliveryCount: 0, tipCount: 0, totalTips: 0, pendingCount: 0 };
      } else {
        console.log(`${logPrefix} PHASE 2A: Address validation successful`);
        currentFlags = validateAndCastFlags(addressData?.addressData?.flags);
        manualDndState = currentFlags?.manualDndState ?? null;
        existingOrderIdsFromDisk = addressData?.addressData?.orderIds || [];
        previousAddressStats = addressData?.addressData?.deliveryStats || {
          deliveryCount: 0, tipCount: 0, totalTips: 0, pendingCount: 0
        };
      }

      console.log(`${logPrefix} PHASE 2B: Processing ${deliveriesSnapshot.size} deliveries`);
      // Process deliveries in a single pass for efficiency
      const processedData = await processDeliveriesData(deliveriesSnapshot, addressId, userDndPrefs, logPrefix, existingOrderIdsFromDisk, manualDndState, hasIncompleteHistory);

      console.log(`${logPrefix} PHASE 2C: Calculating deltas for user profile updates`);
      // ✅ USER PROFILE DELTA CALCULATION: Calculate deltas for atomic user profile updates with null safety
      const deliveryCountDelta = (processedData.deliveryCount || 0) - (previousAddressStats.deliveryCount || 0);
      const tipCountDelta = (processedData.tipCount || 0) - (previousAddressStats.tipCount || 0);
      const totalTipsDelta = processedData.totalTips - (previousAddressStats.totalTips || 0);
      const pendingCountDelta = (processedData.pendingCount || 0) - (previousAddressStats.pendingCount || 0);

      console.log(`${logPrefix} CALCULATED DELTAS: deliveryCount=${deliveryCountDelta}, tipCount=${tipCountDelta}, totalTips=${totalTipsDelta}, pendingCount=${pendingCountDelta}`);

      console.log(`${logPrefix} PHASE 3: Executing full transaction with computed data`);

      const newStats = {
        deliveryCount: processedData.deliveryCount,
        tipCount: processedData.tipCount,
        totalTips: processedData.totalTips,
        averageTipAmount: processedData.calculatedAverageTip,
        highestTip: processedData.highestTip,
        pendingCount: processedData.pendingCount,
        averageTimeMinutes: processedData.calculatedAverageTime,
        lastDeliveryDate: processedData.lastDeliveryTimestamp,
        lastDeliveryTimestamp: processedData.lastDeliveryTimestamp,
      } as const;

      console.log(`${logPrefix} PHASE 3A: Starting full transaction with all updates`);
      
      // ✅ PAYLOAD LOGGING: Log computed stats before transaction
      console.log(`${logPrefix} COMPUTED STATS:`, JSON.stringify(newStats, null, 2));
      console.log(`${logPrefix} PROCESSED DATA SUMMARY:`, JSON.stringify({
        deliveryCount: processedData.deliveryCount,
        tipCount: processedData.tipCount,
        pendingCount: processedData.pendingCount,
        totalTips: processedData.totalTips,
        highestTip: processedData.highestTip,
        averageTip: processedData.calculatedAverageTip,
        finalDndStatus: processedData.finalAddressDndStatus,
        dndSource: processedData.finalAddressDndSource,
        collectedOrderIdsCount: processedData.collectedOrderIds.size,
        targetDeliveryId: processedData.targetDeliveryForUpdate?.id || null
      }, null, 2));
      
      await db.runTransaction(async (transaction) => {
        console.log(`${logPrefix} TRANSACTION: Reading documents for optimistic concurrency control`);

        // ✅ CRITICAL: Read documents inside transaction for proper optimistic concurrency control
        const [transactionAddressSnap, transactionUserProfileSnap] = await Promise.all([
          transaction.get(addressRef),
          transaction.get(userProfileRef)
        ]);

        if (!transactionAddressSnap.exists) {
          throw new Error(`Address ${addressId} not found in transaction for user ${userId}`);
        }

        console.log(`${logPrefix} TRANSACTION: Snapshots acquired, performing writes`);

        // ✅ WRITE 1: Update address document
        const addressUpdateData: any = {
          addressData: {
            deliveryStats: newStats,
            orderIds: Array.from(processedData.collectedOrderIds),
            flags: {
              doNotDeliver: processedData.finalAddressDndStatus,
              isVerified: processedData.finalAddressVerifiedStatus,
              dndSource: processedData.finalAddressDndSource,
            },
            metadata: {
              updatedAt: FieldValue.serverTimestamp(),
            }
          }
        };

        // ✅ PAYLOAD LOGGING: Log exact payload being written
        console.log(`${logPrefix} WRITE PAYLOAD 1 - Address Update:`, JSON.stringify({
          addressId: addressId,
          payload: {
            ...addressUpdateData,
            addressData: {
              ...addressUpdateData.addressData,
              metadata: {
                ...addressUpdateData.addressData.metadata,
                updatedAt: "[FieldValue.serverTimestamp()]" // Replace for logging
              }
            }
          }
        }, null, 2));

        transaction.set(addressRef, addressUpdateData, { merge: true });

        // ✅ WRITE 2: Update user profile document with PROPER NESTED STRUCTURE
        const userProfileUpdateData: any = {
          profileData: {
            usage: {
              lastUsageUpdate: FieldValue.serverTimestamp(),
              ...(deliveryCountDelta !== 0 && { deliveryCount: FieldValue.increment(deliveryCountDelta) }),
              ...(deliveryCountDelta > 0 && (previousAddressStats.deliveryCount || 0) === 0 && { addressCount: FieldValue.increment(1) })
            },
            usageStats: {
              lastUsageDate: processedData.lastDeliveryTimestamp || FieldValue.serverTimestamp(),
              ...(deliveryCountDelta !== 0 && { deliveryCount: FieldValue.increment(deliveryCountDelta) }),
              ...(tipCountDelta !== 0 && { tipCount: FieldValue.increment(tipCountDelta) }),
              ...(totalTipsDelta !== 0 && { totalTips: FieldValue.increment(totalTipsDelta) }),
              ...(deliveryCountDelta > 0 && (previousAddressStats.deliveryCount || 0) === 0 && { addressCount: FieldValue.increment(1) })
            },
            metadata: {
              updatedAt: FieldValue.serverTimestamp()
            }
          }
        };

        // ✅ PAYLOAD LOGGING: Log user profile payload
        console.log(`${logPrefix} WRITE PAYLOAD 2 - User Profile Update:`, JSON.stringify({
          userId: userId,
          deltas: { deliveryCountDelta, tipCountDelta, totalTipsDelta, pendingCountDelta },
          payload: {
            ...userProfileUpdateData,
            profileData: {
              ...userProfileUpdateData.profileData,
              usage: {
                ...userProfileUpdateData.profileData.usage,
                lastUsageUpdate: "[FieldValue.serverTimestamp()]"
              },
              usageStats: {
                ...userProfileUpdateData.profileData.usageStats,
                lastUsageDate: userProfileUpdateData.profileData.usageStats.lastUsageDate instanceof Timestamp ? userProfileUpdateData.profileData.usageStats.lastUsageDate.toDate().toISOString() : "[FieldValue.serverTimestamp()]"
              },
              metadata: {
                updatedAt: "[FieldValue.serverTimestamp()]"
              }
            }
          }
        }, null, 2));

        transaction.set(userProfileRef, userProfileUpdateData, { merge: true });

        // ✅ WRITE 3: Update delivery document if needed
        if (processedData.manualDndState === 'FORCE_DND' || processedData.manualDndState === 'FORCE_ALLOW') {
          console.log(`${logPrefix} FAST-PATH: Skipping delivery-level flag update for manual override`);
        } else if (deliveryId) {
          // Update specific delivery when deliveryId is provided
          const deliveryRef = db.collection('users').doc(userId).collection('user_deliveries').doc(deliveryId);
          const deliveryUpdateData = {
            deliveryData: {
              status: {
                doNotDeliver: processedData.finalAddressDndStatus,
                dndReason: processedData.finalAddressDndSource,
              },
              metadata: {
                updatedAt: FieldValue.serverTimestamp(),
                updatedByStatsFlow: true
              }
            }
          };

          console.log(`${logPrefix} WRITE PAYLOAD 3 - Delivery Update:`, JSON.stringify({
            deliveryId: deliveryId,
            payload: {
              ...deliveryUpdateData,
              deliveryData: {
                ...deliveryUpdateData.deliveryData,
                metadata: {
                  ...deliveryUpdateData.deliveryData.metadata,
                  updatedAt: "[FieldValue.serverTimestamp()]"
                }
              }
            }
          }, null, 2));

          transaction.set(deliveryRef, deliveryUpdateData, { merge: true });
          console.log(`${logPrefix} Updated specific delivery ${deliveryId} with DND status: ${processedData.finalAddressDndStatus}`);
        } else if (processedData.targetDeliveryForUpdate) {
          // Update most recent delivery found during processing
          const currentDeliveryData = processedData.targetDeliveryForUpdate.data?.deliveryData;
          if (currentDeliveryData) {
            const currentStatus = validateAndCastStatus(currentDeliveryData.status);
            const currentDndStatus = currentStatus?.doNotDeliver ?? false;
            const currentDndReason = currentStatus?.dndReason ?? null;

            if (processedData.finalAddressDndStatus !== currentDndStatus || processedData.finalAddressDndSource !== currentDndReason) {
              const deliveryRef = db.collection('users').doc(userId).collection('user_deliveries').doc(processedData.targetDeliveryForUpdate.id);
              const deliveryUpdateData = {
                deliveryData: {
                  status: {
                    doNotDeliver: processedData.finalAddressDndStatus,
                    dndReason: processedData.finalAddressDndSource,
                  },
                  metadata: {
                    updatedAt: FieldValue.serverTimestamp(),
                    updatedByStatsFlow: true
                  }
                }
              };

              console.log(`${logPrefix} WRITE PAYLOAD 3 - Target Delivery Update:`, JSON.stringify({
                deliveryId: processedData.targetDeliveryForUpdate.id,
                payload: {
                  ...deliveryUpdateData,
                  deliveryData: {
                    ...deliveryUpdateData.deliveryData,
                    metadata: {
                      ...deliveryUpdateData.deliveryData.metadata,
                      updatedAt: "[FieldValue.serverTimestamp()]"
                    }
                  }
                }
              }, null, 2));

              transaction.set(deliveryRef, deliveryUpdateData, { merge: true });
              console.log(`${logPrefix} Updated most recent delivery ${processedData.targetDeliveryForUpdate.id} with DND status: ${processedData.finalAddressDndStatus} (was: ${currentDndStatus})`);
            } else {
              console.log(`${logPrefix} DND status unchanged for delivery ${processedData.targetDeliveryForUpdate.id}. Skipping delivery update.`);
            }
          }
        }

        console.log(`${logPrefix} TRANSACTION: All writes queued successfully`);
      });
      
      console.log(`${logPrefix} PHASE 3A: Transaction completed successfully`);

      // ✅ ENHANCED METRICS: Complete monitoring with all updates
      console.log(`${logPrefix} SUCCESS SUMMARY:`);
      console.log(`${logPrefix} - Address Stats: deliveryCount=${processedData.deliveryCount}, tipCount=${processedData.tipCount}, totalTips=${processedData.totalTips}, pendingCount=${processedData.pendingCount}`);
      console.log(`${logPrefix} - User Profile Deltas: deliveryCount=${deliveryCountDelta}, tipCount=${tipCountDelta}, totalTips=${totalTipsDelta}`);
      console.log(`${logPrefix} - DND Status: ${processedData.finalAddressDndStatus}, Source: ${processedData.finalAddressDndSource}`);
      console.log(`${logPrefix} Successfully updated address stats, user profile, and delivery documents for addressId: ${addressId}`);
      
      return { addressId, status: "Success", updatedStats: newStats };
    } catch (error: any) {
      console.error(`${logPrefix} ERROR during address stats recalculation:`, error.message, error.stack);
      return { addressId, status: `Error: ${error.message}`, updatedStats: null };
    }
  }
); 