import * as z from 'zod';
import { defineFlow } from '@genkit-ai/flow';
import { getFirestore, Timestamp, FieldValue } from 'firebase-admin/firestore';
import { dndPreferencesCache, ComputedDndSettings } from '../utils/dnd-preferences-cache';

// ✅ PERFORMANCE: Initialize Firestore connection at module load to reduce cold start impact
const db = getFirestore();

// ✅ COMPREHENSIVE IMPORTS: Use all relevant generated types
import type {
  Address,
  DeliveryStats,
  Flags,
  Coordinates,
  Components
} from '../models/generated/address.schema';

import type {
  Delivery,
  Amounts,
  Reference
} from '../models/generated/delivery.schema';

import type {
  Status
} from '../models/generated/status.schema';

import type {
  UserProfileSchema
} from '../models/generated/user_profile.schema';

// ✅ SIMPLIFIED: Basic runtime validation with graceful degradation
function validateWithFallback<T>(data: any, entityType: string): { data: T; errors: string[] } {
  const errors: string[] = [];

  if (!data) {
    errors.push(`${entityType} data is null/undefined`);
    return { data: {} as T, errors };
  }

  // Continue with data, log errors but don't fail
  return { data: data as T, errors };
}

// ✅ SIMPLIFIED: Basic address validation with graceful degradation
export function validateAndCastAddress(data: any): Address | null {
  const validation = validateWithFallback<Address>(data, 'Address');
  if (validation.errors.length > 0) {
    console.warn('Address validation warnings:', validation.errors);
  }
  return validation.data;
}

// ✅ SIMPLIFIED: Basic delivery validation with graceful degradation
export function validateAndCastDelivery(data: any, docId?: string): Delivery | null {
  const deliveryData = data?.deliveryData || data;
  const validation = validateWithFallback<Delivery>({ deliveryData }, 'Delivery');
  if (validation.errors.length > 0) {
    console.warn('Delivery validation warnings:', validation.errors);
  }
  return {
    ...validation.data,
    id: docId || validation.data.id || ''
  } as Delivery;
}

export function validateAndCastFlags(data: any): Flags | null {
  const validation = validateWithFallback<Flags>(data, 'Flags');
  if (validation.errors.length > 0) {
    console.warn('Flags validation warnings:', validation.errors);
  }
  return validation.data;
}

export function validateAndCastStatus(data: any): Status | null {
  const validation = validateWithFallback<Status>(data, 'Status');
  if (validation.errors.length > 0) {
    console.warn('Status validation warnings:', validation.errors);
  }
  return validation.data;
}




const FlowOutputSchema = z.object({
  addressId: z.string().nullable(),
  status: z.string(),
  updatedStats: z.any().nullable(),
});

// ✅ SIMPLIFIED: Basic timestamp parsing
function parseTimestamp(timestampInput: any): Timestamp | null {
  if (!timestampInput) return null;
  if (timestampInput instanceof Timestamp) return timestampInput;
  if (timestampInput && typeof timestampInput.toDate === 'function') {
    return Timestamp.fromDate(timestampInput.toDate());
  }
  return null;
}

export const AddressStatsUpdateInputSchema = z.object({
  userId: z.string(),
  addressId: z.string(),
  deliveryId: z.string().optional(),
});

// ✅ ENHANCED: Calculate comprehensive address stats using rich data models
function calculateAddressStats(deliveries: Delivery[]): {
  deliveryCount: number;
  tipCount: number;
  pendingCount: number;
  totalTips: number;
  highestTip: number | null;
  lastDeliveryTimestamp: Timestamp | null;
  averageTipAmount: number;
  averageTimeMinutes: number | null;
  collectedOrderIds: string[];
  // Enhanced stats using rich data
  totalBasePay: number;
  totalFinalPay: number;
  averageDistance: number | null;
  verifiedDeliveryCount: number;
  cancelledDeliveryCount: number;
  platformBreakdown: { [platform: string]: number };
  currencyBreakdown: { [currency: string]: number };
} {
  let deliveryCount = 0;
  let tipCount = 0;
  let pendingCount = 0;
  let totalTips = 0;
  let totalBasePay = 0;
  let totalFinalPay = 0;
  let totalDistance = 0;
  let deliveriesWithDistance = 0;
  let verifiedDeliveryCount = 0;
  let cancelledDeliveryCount = 0;
  let highestTip: number | null = null;
  let lastDeliveryTimestamp: Timestamp | null = null;
  let totalDeliveryTimeMinutes = 0;
  let deliveriesWithTime = 0;
  const collectedOrderIds: string[] = [];
  const platformBreakdown: { [platform: string]: number } = {};
  const currencyBreakdown: { [currency: string]: number } = {};

  for (const delivery of deliveries) {
    deliveryCount++;
    collectedOrderIds.push(delivery.deliveryData.orderId);

    // Enhanced status analysis
    const status = delivery.deliveryData?.status;
    const amounts = delivery.deliveryData?.amounts;
    const platform = delivery.deliveryData?.platform;

    const tipAmount = amounts?.tipAmount;
    const basePay = amounts?.basePay;
    const finalPay = amounts?.finalPay;
    const distanceMiles = amounts?.distanceMiles;
    const currencyCode = amounts?.currencyCode || 'USD';

    // More precise completion detection using multiple status fields
    const isCompleted = status?.isCompleted || status?.isTipped || status?.state === 'completed';
    const isVerified = status?.isVerified;
    const isCancelled = status?.state === 'cancelled' || status?.cancellationReason != null;

    // Track platform usage
    if (platform?.name) {
      platformBreakdown[platform.name] = (platformBreakdown[platform.name] || 0) + 1;
    }

    // Track currency usage
    currencyBreakdown[currencyCode] = (currencyBreakdown[currencyCode] || 0) + 1;

    // Enhanced financial tracking
    if (basePay != null) {
      totalBasePay += basePay;
    }

    if (finalPay != null) {
      totalFinalPay += finalPay;
    }

    if (distanceMiles != null && distanceMiles > 0) {
      totalDistance += distanceMiles;
      deliveriesWithDistance++;
    }

    if (isVerified) {
      verifiedDeliveryCount++;
    }

    if (isCancelled) {
      cancelledDeliveryCount++;
    }

    // Enhanced tip analysis
    if (tipAmount != null && isCompleted) {
      tipCount++;
      totalTips += tipAmount;
      if (highestTip === null || tipAmount > highestTip) {
        highestTip = tipAmount;
      }
    } else {
      pendingCount++;
    }

    // Track latest timestamp with enhanced logic
    const completedAt = delivery.deliveryData?.times?.completedAt;
    const tippedAt = delivery.deliveryData?.times?.tippedAt;
    const createdAt = delivery.deliveryData?.metadata?.createdAt;
    const timestamp = completedAt || tippedAt || createdAt;

    if (timestamp && (!lastDeliveryTimestamp || timestamp.toMillis() > lastDeliveryTimestamp.toMillis())) {
      lastDeliveryTimestamp = timestamp;
    }

    // Enhanced delivery time calculation
    const acceptedAt = delivery.deliveryData?.times?.acceptedAt;

    if (acceptedAt && completedAt && acceptedAt instanceof Timestamp && completedAt instanceof Timestamp) {
      const deliveryTimeMs = completedAt.toMillis() - acceptedAt.toMillis();
      if (deliveryTimeMs > 0) {
        totalDeliveryTimeMinutes += deliveryTimeMs / (1000 * 60);
        deliveriesWithTime++;
      }
    }
  }

  return {
    deliveryCount,
    tipCount,
    pendingCount,
    totalTips,
    averageTipAmount: tipCount > 0 ? totalTips / tipCount : 0,
    averageTimeMinutes: deliveriesWithTime > 0 ? totalDeliveryTimeMinutes / deliveriesWithTime : null,
    highestTip,
    lastDeliveryTimestamp,
    collectedOrderIds,
    // Enhanced stats
    totalBasePay,
    totalFinalPay,
    averageDistance: deliveriesWithDistance > 0 ? totalDistance / deliveriesWithDistance : null,
    verifiedDeliveryCount,
    cancelledDeliveryCount,
    platformBreakdown,
    currencyBreakdown
  };
}

// ✅ SIMPLIFIED: DND evaluation using existing architecture
function evaluateAddressDnd(
  deliveries: Delivery[],
  userDndPrefs: ComputedDndSettings,
  manualState: string | null
): { dnd: boolean; source: string | null } {
  // 1. Manual override (highest priority)
  if (manualState === 'FORCE_DND') return { dnd: true, source: 'MANUAL_USER_FORCE_DND' };
  if (manualState === 'FORCE_ALLOW') return { dnd: false, source: 'MANUAL_USER_FORCE_ALLOW' };

  // 2. Use existing DND evaluation logic
  const deliveryDataArray = deliveries.map(d => d.deliveryData);
  const result = evaluateDndForDeliveryData(deliveryDataArray, userDndPrefs);

  if (result.dnd) {
    return {
      dnd: true,
      source: result.reason === 'EXPLICIT_IMPORT' ? 'RULE_BASED_EXPLICIT_IMPORT' : 'RULE_BASED_USER_PREFERENCES'
    };
  }

  return { dnd: false, source: null };
}

// ✅ SIMPLIFIED: DND evaluation using existing architecture
export function evaluateDndForDeliveryData(
    allDeliveryData: Delivery['deliveryData'][],
    userDndPrefs: ComputedDndSettings,
    hasIncompleteHistory = false
): { dnd: boolean; reason: "EXPLICIT_IMPORT" | "RULE_BASED" | null } {
    if (allDeliveryData.length === 0) {
        return { dnd: false, reason: null };
    }

    // 1. Check for explicit import DND flag
    const hasExplicitImportDnd = allDeliveryData.some(deliveryData => {
        const status = validateAndCastStatus(deliveryData.status);
        return status?.dndReason === 'EXPLICIT_IMPORT';
    });

    if (hasExplicitImportDnd) {
        return { dnd: true, reason: 'EXPLICIT_IMPORT' };
    }

    // 2. Analyze confirmed deliveries (exclude pending)
    const confirmedDeliveries = allDeliveryData.filter(deliveryData => {
        const tipAmount = deliveryData.amounts?.tipAmount ?? null;
        const status = validateAndCastStatus(deliveryData.status);
        const isCompleted = status?.isCompleted ?? false;

        // Only confirmed deliveries (tipAmount set AND completed)
        return tipAmount != null && isCompleted;
    });

    if (confirmedDeliveries.length === 0) {
        return { dnd: false, reason: null };
    }

    // 3. Apply DND rules
    if (userDndPrefs.isPremiumUser && userDndPrefs.customRule?.isEnabled) {
        // Premium users: Custom threshold
        const threshold = userDndPrefs.customRule.tipAmountThreshold;
        const lowTipCount = confirmedDeliveries.filter(d => (d.amounts?.tipAmount ?? 0) <= threshold).length;

        if (confirmedDeliveries.length >= 2 && lowTipCount >= 1) {
            return { dnd: true, reason: 'RULE_BASED' };
        }
    } else if (userDndPrefs.defaultRuleApplies) {
        // Freemium users: $0 tips trigger DND
        const zeroTipCount = confirmedDeliveries.filter(d => d.amounts?.tipAmount === 0).length;

        if (zeroTipCount >= 1) {
            return { dnd: true, reason: 'RULE_BASED' };
        }
    }

    return { dnd: false, reason: null };
}

export const updateAddressDeliveryStatsFlow = defineFlow(
  {
    name: 'updateAddressDeliveryStatsFlow',
    inputSchema: AddressStatsUpdateInputSchema,
    outputSchema: FlowOutputSchema,
  },
  async (input) => {
    const { userId, addressId, deliveryId } = input;
    const logPrefix = `[AddressStatsFlow ${userId}/${addressId}${deliveryId ? `/${deliveryId}` : ''}]`;

    console.log(`${logPrefix} Starting address delivery stats update`);

    if (!userId || !addressId) {
      console.error(`${logPrefix} Missing required parameters: userId=${userId}, addressId=${addressId}`);
      return { addressId: addressId || null, status: "Error: Missing userId or addressId", updatedStats: null };
    }

    try {
      console.log(`${logPrefix} PHASE 1: Starting parallel data fetching`);
      // PHASE 1: Parallel data fetching (following successful pattern)
      const [addressDoc, deliveriesSnapshot, userDndPrefs] = await Promise.all([
        db.collection('users').doc(userId).collection('user_addresses').doc(addressId).get(),
        db.collection('users').doc(userId).collection('user_deliveries')
          .where('deliveryData.reference.addressId', '==', addressId)
          .limit(50)
          .get(),
        dndPreferencesCache.getUserDndPreferences(userId, logPrefix).catch(() => ({
          isPremiumUser: false,
          onboardingCompleted: false,
          customRule: { isEnabled: false, tipAmountThreshold: 0, comparisonType: 'less_than_or_equal_to' as const },
          defaultRuleApplies: true
        }))
      ]);

      console.log(`${logPrefix} PHASE 1: Fetched ${deliveriesSnapshot.size} deliveries`);

      if (!addressDoc.exists) {
        console.warn(`${logPrefix} Address not found - will create new address document`);
      }

      console.log(`${logPrefix} PHASE 2: Starting heavy computation outside transaction`);
      // PHASE 2: Heavy computation OUTSIDE transaction
      const deliveries = deliveriesSnapshot.docs.map(doc => ({
        id: doc.id,
        deliveryData: doc.data().deliveryData
      })) as Delivery[];

      const currentAddress = addressDoc.data();
      const manualDndState = currentAddress?.addressData?.flags?.manualDndState || null;

      // Get previous stats for delta calculation
      const previousStats = currentAddress?.addressData?.deliveryStats || {
        deliveryCount: 0,
        tipCount: 0,
        totalTips: 0,
        pendingCount: 0
      };

      // Calculate stats using simple aggregation
      const newStats = calculateAddressStats(deliveries);

      // Calculate deltas for user profile updates (handles dynamic tip changes)
      const deliveryCountDelta = newStats.deliveryCount - (previousStats.deliveryCount || 0);
      const tipCountDelta = newStats.tipCount - (previousStats.tipCount || 0);
      const totalTipsDelta = newStats.totalTips - (previousStats.totalTips || 0);
      const isFirstDeliveryToAddress = (previousStats.deliveryCount || 0) === 0 && newStats.deliveryCount > 0;

      // Handle edge cases for dynamic tip changes
      const hasSignificantChanges = Math.abs(deliveryCountDelta) > 0 || Math.abs(tipCountDelta) > 0 || Math.abs(totalTipsDelta) > 0.01;

      console.log(`${logPrefix} DELTA ANALYSIS: delivery=${deliveryCountDelta}, tip=${tipCountDelta}, totalTips=${totalTipsDelta.toFixed(2)}, significant=${hasSignificantChanges}`);

      // Evaluate DND using existing architecture
      const dndResult = evaluateAddressDnd(deliveries, userDndPrefs, manualDndState);

      console.log(`${logPrefix} DND RESULT: ${dndResult.dnd} (${dndResult.source})`);

      console.log(`${logPrefix} PHASE 3: Starting comprehensive transaction with retroactive updates`);
      // PHASE 3: Comprehensive transaction with address, user profile, and delivery updates
      await db.runTransaction(async (transaction) => {
        const addressRef = db.collection('users').doc(userId).collection('user_addresses').doc(addressId);
        const userProfileRef = db.collection('users').doc(userId);

        // 1. Update address document with comprehensive stats (use set with merge to handle creation)
        transaction.set(addressRef, {
          addressData: {
            deliveryStats: {
              deliveryCount: newStats.deliveryCount,
              tipCount: newStats.tipCount,
              totalTips: newStats.totalTips,
              pendingCount: newStats.pendingCount,
              averageTipAmount: newStats.averageTipAmount,
              averageTimeMinutes: newStats.averageTimeMinutes,
              highestTip: newStats.highestTip,
              lastDeliveryTimestamp: newStats.lastDeliveryTimestamp,
              // Enhanced stats from rich data models
              totalBasePay: newStats.totalBasePay,
              totalFinalPay: newStats.totalFinalPay,
              averageDistance: newStats.averageDistance,
              verifiedDeliveryCount: newStats.verifiedDeliveryCount,
              cancelledDeliveryCount: newStats.cancelledDeliveryCount,
              platformBreakdown: newStats.platformBreakdown,
              currencyBreakdown: newStats.currencyBreakdown
            },
            orderIds: newStats.collectedOrderIds,
            flags: {
              doNotDeliver: dndResult.dnd,
              dndSource: dndResult.source,
              isVerified: dndResult.dnd,
            },
            metadata: {
              updatedAt: FieldValue.serverTimestamp()
            }
          }
        }, { merge: true });

        // 2. Update user profile with comprehensive deltas (handles positive and negative changes)
        if (hasSignificantChanges || isFirstDeliveryToAddress) {
          // User profile update with FieldValue operations (requires any for Firestore operations)
          const userProfileUpdate: any = {
            profileData: {
              usage: {
                lastUsageUpdate: FieldValue.serverTimestamp()
              },
              usageStats: {
                lastUsageDate: newStats.lastDeliveryTimestamp || FieldValue.serverTimestamp(),
                // Enhanced stats tracking using rich data
                totalRuns: FieldValue.increment(1), // Track each stats update as a "run"
                activeDaysCount: FieldValue.increment(1) // Could be enhanced with date logic
              },
              metadata: {
                updatedAt: FieldValue.serverTimestamp()
              }
            }
          };

          // Conditional increments for changed values
          if (deliveryCountDelta !== 0) {
            (userProfileUpdate.profileData!.usage as any).deliveryCount = FieldValue.increment(deliveryCountDelta);
            (userProfileUpdate.profileData!.usageStats as any).deliveryCount = FieldValue.increment(deliveryCountDelta);
          }

          if (tipCountDelta !== 0) {
            (userProfileUpdate.profileData!.usageStats as any).tipCount = FieldValue.increment(tipCountDelta);
          }

          if (totalTipsDelta !== 0) {
            (userProfileUpdate.profileData!.usageStats as any).totalTips = FieldValue.increment(totalTipsDelta);
          }

          if (isFirstDeliveryToAddress) {
            (userProfileUpdate.profileData!.usageStats as any).addressCount = FieldValue.increment(1);
          }

          transaction.set(userProfileRef, userProfileUpdate, { merge: true });
          console.log(`${logPrefix} Updated user profile with deltas: delivery=${deliveryCountDelta}, tip=${tipCountDelta}, totalTips=${totalTipsDelta.toFixed(2)}`);
        }

        // 3. Update ALL delivery documents for this address with DND status (retroactive)
        console.log(`${logPrefix} Updating ${deliveries.length} delivery documents with DND status: ${dndResult.dnd}`);

        for (const delivery of deliveries) {
          if (delivery.id) {
            const deliveryRef = db.collection('users').doc(userId).collection('user_deliveries').doc(delivery.id);

            // Only update if DND status actually changed
            const currentDndStatus = delivery.deliveryData?.status?.doNotDeliver ?? false;
            const currentDndReason = delivery.deliveryData?.status?.dndReason ?? null;

            if (currentDndStatus !== dndResult.dnd || currentDndReason !== dndResult.source) {
              transaction.set(deliveryRef, {
                deliveryData: {
                  status: {
                    doNotDeliver: dndResult.dnd,
                    dndReason: dndResult.source
                  },
                  metadata: {
                    updatedAt: FieldValue.serverTimestamp(),
                    updatedByStatsFlow: true
                  }
                }
              }, { merge: true });
            }
          }
        }
      });

      console.log(`${logPrefix} SUCCESS: Updated address, user profile, and ${deliveries.length} delivery documents`);
      console.log(`${logPrefix} ENHANCED STATS: basePay=${newStats.totalBasePay}, finalPay=${newStats.totalFinalPay}, avgDistance=${newStats.averageDistance?.toFixed(2)}mi, verified=${newStats.verifiedDeliveryCount}, cancelled=${newStats.cancelledDeliveryCount}`);

      return {
        addressId,
        status: "Success",
        updatedStats: {
          // Core stats
          deliveryCount: newStats.deliveryCount,
          tipCount: newStats.tipCount,
          totalTips: newStats.totalTips,
          pendingCount: newStats.pendingCount,
          averageTipAmount: newStats.averageTipAmount,
          averageTimeMinutes: newStats.averageTimeMinutes,
          highestTip: newStats.highestTip,
          lastDeliveryTimestamp: newStats.lastDeliveryTimestamp,
          // Enhanced stats from rich data models
          totalBasePay: newStats.totalBasePay,
          totalFinalPay: newStats.totalFinalPay,
          averageDistance: newStats.averageDistance,
          verifiedDeliveryCount: newStats.verifiedDeliveryCount,
          cancelledDeliveryCount: newStats.cancelledDeliveryCount,
          platformBreakdown: newStats.platformBreakdown,
          currencyBreakdown: newStats.currencyBreakdown
        }
      };

    } catch (error: any) {
      console.error(`${logPrefix} ERROR:`, error.message);
      return { addressId, status: `Error: ${error.message}`, updatedStats: null };
    }
  }
);