# Dual Interface Orchestration Pattern: Technical Implementation Guide

**Supplement to clarity.md** - Concrete implementation examples and architectural patterns for proper repository dual interface orchestration.

## Overview

This document provides detailed technical guidance for implementing the Dual Interface Orchestration Pattern established in clarity.md, using the **UserProfileRepositoryImpl** as the reference implementation that demonstrates perfect architectural alignment.

## Core Architectural Principle

**Single Implementation, Dual Purpose**: One repository implementation class serves both domain business logic needs and comprehensive data infrastructure needs through separate interface contracts.

```kotlin
// ✅ CORRECT PATTERN: UserProfileRepositoryImpl
class UserProfileRepositoryImpl(
    private val remoteDataSource: UserRemoteDataSource,
    private val localDataSource: UserLocalDataSource,
    private val userMapper: UserMapper,
    // ... other dependencies
) : UserRepository, UserProfileRepository {
    // Implements BOTH interfaces in single class
}
```

## Interface Architecture Breakdown

### 1. Domain Interface Pattern (Business-Focused)

**Purpose**: Essential operations for ViewModels and UseCases
**File**: `domain/repository/UserRepository.kt`
**Size**: 162 lines - focused and essential

```kotlin
interface UserRepository {
    // ===== CORE CRUD OPERATIONS =====
    suspend fun getUserById(id: String): Result<User?>
    suspend fun getCurrentUser(): Result<User?>
    suspend fun addUser(user: User): Result<Unit>
    suspend fun updateUser(user: User): Result<Unit>
    suspend fun deleteUser(id: String): Result<Unit>

    // ===== REACTIVE OPERATIONS =====
    fun observeUserById(id: String): Flow<Result<User?>>
    fun observeCurrentUser(): Flow<Result<User?>>

    // ===== ESSENTIAL BUSINESS OPERATIONS =====
    suspend fun updateUserPreferences(userId: String, preferences: UserPreferences): Result<Unit>
    suspend fun updateUserSubscription(userId: String, subscription: UserSubscription): Result<Unit>
    suspend fun setDefaultAddress(userId: String, addressId: String): Result<Unit>
    
    // ===== VALIDATION AND UTILITY =====
    suspend fun validateUser(user: User): Result<Unit>
    suspend fun userExistsByEmail(email: String): Result<Boolean>
    suspend fun createDefaultUser(userId: String, email: String?): Result<User>
}
```

**Key Characteristics**:
- **Essential Operations Only**: Core CRUD, reactive streams, primary business logic
- **Domain Model Focus**: Uses SSoT models (`User`, `UserPreferences`, `UserSubscription`)
- **Business Logic**: Operations that ViewModels and UseCases directly need
- **Clean Contract**: No infrastructure concerns (cache, export, backup)

### 2. Data Interface Pattern (Infrastructure-Focused)

**Purpose**: Comprehensive data management for infrastructure components
**File**: `data/repository/user/UserProfileRepository.kt`
**Size**: 76 lines - comprehensive infrastructure

```kotlin
interface UserProfileRepository {
    // ===== SHARED CORE OPERATIONS (IDENTICAL TO DOMAIN) =====
    suspend fun getUserById(id: String): Result<User?>
    suspend fun getCurrentUser(): Result<User?>
    suspend fun addUser(user: User): Result<Unit>
    suspend fun updateUser(user: User): Result<Unit>
    suspend fun deleteUser(id: String): Result<Unit>
    fun observeUserById(id: String): Flow<Result<User?>>
    fun observeCurrentUser(): Flow<Result<User?>>

    // ===== INFRASTRUCTURE-SPECIFIC OPERATIONS =====
    
    // Cache Management
    suspend fun prefetchUserData(userId: String): Result<Unit>
    suspend fun clearCache(userId: String): Result<Unit>
    suspend fun clearAllCache(): Result<Unit>
    suspend fun invalidateCache(userId: String): Result<Unit>
    suspend fun refreshFromRemote(userId: String): Result<Unit>

    // Import/Export Operations
    suspend fun exportUserData(userId: String, format: String = "json"): Result<String>
    suspend fun importUserData(userId: String, data: Map<String, Any>): Result<Unit>

    // Backup and Recovery
    suspend fun createUserBackup(userId: String): Result<Map<String, Any>>
    suspend fun restoreUserBackup(userId: String, backup: Map<String, Any>): Result<Unit>
    suspend fun migrateUserData(userId: String, fromVersion: Long, toVersion: Long): Result<Unit>

    // Repository Lifecycle
    suspend fun initialize(): Result<Unit>
    suspend fun cleanup(): Result<Unit>
}
```

**Key Characteristics**:
- **Comprehensive Coverage**: All domain methods PLUS infrastructure operations
- **Infrastructure Focus**: Cache management, import/export, backup/recovery, lifecycle
- **System Integration**: Used by CacheLifecycleManager, CacheWarmingManager, maintenance tools
- **Operational Completeness**: Everything needed for data layer management

## Implementation Architecture

### 3. Repository Implementation Pattern

**File**: `data/repository/user/UserProfileRepositoryImpl.kt`
**Purpose**: Single orchestrator implementing both interfaces

```kotlin
class UserProfileRepositoryImpl(
    // Core architectural dependencies following clarity.md principles
    private val remoteDataSource: UserRemoteDataSource,
    private val localDataSource: UserLocalDataSource,
    private val userMapper: UserMapper,
    // Repository cross-domain dependencies
    private val addressRepository: AddressRepository,
    // Infrastructure utilities
    private val authManager: AuthenticationManager,
    private val ioDispatcher: CoroutineDispatcher,
    private val applicationScope: CoroutineScope,
    // Lazy infrastructure utilities to prevent ANR
    private val requestDeduplicationManager: Lazy<RequestDeduplicationManager>,
    private val priorityTaskScheduler: Lazy<ModernPriorityTaskScheduler>,
    private val sessionManager: Lazy<SessionManager>,
    private val cacheWarmingManager: Lazy<CacheWarmingManager>,
    private val authStateCoordinator: Lazy<AuthenticationStateCoordinator>,
    private val repositoryErrorHandler: Lazy<RepositoryErrorHandler>,
    private val validationEngine: Lazy<ValidationEngine>
) : UserRepository, UserProfileRepository {

    // ===== DOMAIN INTERFACE IMPLEMENTATION =====
    // Business-focused methods for ViewModels and UseCases
    
    override suspend fun getCurrentUser(): Result<User?> = withContext(ioDispatcher) {
        // Enhanced error handling with RepositoryErrorHandler
        val handlerResult = repositoryErrorHandler.value.handleSuspendFunction(
            operationName = "getCurrentUser",
            entityType = "User"
        ) {
            val currentUserId = getCurrentUserIdSuspend()
            
            // ✅ ENHANCED: Trigger cache warming for critical user data
            applicationScope.launch(ioDispatcher) {
                try {
                    cacheWarmingManager.value.warmCriticalData(currentUserId)
                } catch (e: Exception) {
                    Log.w(TAG, "Cache warming failed for user $currentUserId", e)
                }
            }

            val result = getUserById(currentUserId)
            return@handleSuspendFunction result
        }
        
        // Convert kotlin.Result to com.autogratuity.data.model.Result
        when {
            handlerResult.isSuccess -> handlerResult.getOrNull() ?: Result.Error(Exception("Null result"))
            else -> Result.Error(handlerResult.exceptionOrNull() as? Exception ?: Exception("Unknown error"))
        }
    }

    // ===== DATA INTERFACE IMPLEMENTATION =====
    // Infrastructure-focused methods for system components

    override suspend fun exportUserData(userId: String, format: String): Result<String> = withContext(ioDispatcher) {
        executeWithEnhancedErrorHandling(
            operation = "exportUserData",
            entityId = userId,
            validateInput = {
                if (userId.isBlank()) throw IllegalArgumentException("User ID cannot be blank")
                if (format.isBlank()) throw IllegalArgumentException("Format cannot be blank")
            }
        ) {
            // Get user data
            val userResult = getUserById(userId)
            when (userResult) {
                is Result.Success -> {
                    val user = userResult.data ?: return@executeWithEnhancedErrorHandling Result.Error(
                        Exception("User not found for export: $userId")
                    )
                    
                    // Export based on format
                    val exportData = when (format.lowercase()) {
                        "json" -> userMapper.exportToJson(user)
                        "csv" -> userMapper.exportToCsv(user)
                        else -> throw IllegalArgumentException("Unsupported export format: $format")
                    }
                    
                    Result.Success(exportData)
                }
                is Result.Error -> userResult
                is Result.Loading -> Result.Error(IllegalStateException("Unexpected Loading state"))
            }
        }
    }
}
```

## Dependency Injection Configuration

### 4. DI Module Pattern

**File**: `di/DataModule.kt` or `di/RepositoryModule.kt`

```kotlin
// ✅ CRITICAL: Both interfaces must be bound to the same implementation instance
single<UserRepository> { 
    UserProfileRepositoryImpl(
        remoteDataSource = get(),
        localDataSource = get(),
        userMapper = get(),
        addressRepository = get(),
        authManager = get(),
        ioDispatcher = get(named("ioDispatcher")),
        applicationScope = get(named("applicationScope")),
        requestDeduplicationManager = lazy { get() },
        priorityTaskScheduler = lazy { get() },
        sessionManager = lazy { get() },
        cacheWarmingManager = lazy { get() },
        authStateCoordinator = lazy { get() },
        repositoryErrorHandler = lazy { get() },
        validationEngine = lazy { get() }
    )
}

// ✅ ESSENTIAL: Data interface bound to same instance via type casting
single<UserProfileRepository> { 
    get<UserRepository>() as UserProfileRepositoryImpl 
}
```

**Critical Points**:
- **Single Instance**: Both interfaces point to the same implementation instance
- **Type Safety**: Casting ensures proper interface implementation
- **Lazy Dependencies**: Prevents circular dependencies and ANR issues
- **Comprehensive DI**: All architectural components properly injected

## Usage Patterns

### 5. Domain Layer Usage (ViewModels, UseCases)

```kotlin
// ✅ ViewModels use Domain Interface
class UserProfileViewModel(
    private val userRepository: UserRepository // Domain interface
) : ViewModel() {
    
    fun loadCurrentUser() {
        viewModelScope.launch {
            val result = userRepository.getCurrentUser() // Business operation
            when (result) {
                is Result.Success -> _userState.value = result.data
                is Result.Error -> _errorState.value = result.exception.message
                is Result.Loading -> _loadingState.value = true
            }
        }
    }
    
    fun updateUserPreferences(preferences: UserPreferences) {
        viewModelScope.launch {
            val currentUser = userRepository.getCurrentUser()
            if (currentUser is Result.Success && currentUser.data != null) {
                userRepository.updateUserPreferences(currentUser.data.id, preferences)
            }
        }
    }
}
```

### 6. Infrastructure Layer Usage (System Components)

```kotlin
// ✅ Infrastructure components use Data Interface
class CacheLifecycleManager(
    private val userProfileRepository: UserProfileRepository, // Data interface
    private val deliveryProfileRepository: DeliveryProfileRepository // Data interface
) {
    
    suspend fun performSystemCleanup() {
        // Use comprehensive data operations
        userProfileRepository.clearAllCache()
        userProfileRepository.cleanup()
        
        deliveryProfileRepository.clearAllCache()
        deliveryProfileRepository.cleanup()
    }
    
    suspend fun createSystemBackup(userId: String): Map<String, Any> {
        return mapOf(
            "userData" to userProfileRepository.createUserBackup(userId),
            "deliveryData" to deliveryProfileRepository.createDeliveryBackup(userId)
        )
    }
}
```

## Architectural Benefits

### 7. Clear Separation of Concerns

**Domain Interface Benefits**:
- **Focused Contract**: Only essential business operations
- **ViewModel Clarity**: Clear, minimal API for UI layer
- **Business Logic**: Domain-specific operations without infrastructure noise
- **Testing**: Easy to mock essential operations

**Data Interface Benefits**:
- **Comprehensive Management**: All infrastructure operations in one place
- **System Integration**: Perfect for cache managers, backup systems
- **Operational Completeness**: Everything needed for data layer management
- **Maintenance**: Clear contract for system-level operations

### 8. Implementation Efficiency

**Single Implementation Advantages**:
- **No Code Duplication**: Shared logic between interfaces
- **Consistent Behavior**: Same implementation serves both purposes
- **Unified Dependencies**: Single set of injected components
- **Atomic Operations**: Business and infrastructure operations can coordinate

**Method Delegation Pattern**:
```kotlin
// Domain methods can delegate to data methods when appropriate
override suspend fun getCurrentUser(): Result<User?> {
    // Domain method delegates to comprehensive data method
    return getUserById(getCurrentUserIdSuspend())
}

// Data methods provide comprehensive implementation
override suspend fun getUserById(id: String): Result<User?> {
    // Full implementation with caching, error handling, monitoring
    return executeWithPriority("getUserById", "User", id) {
        // Cache-first strategy with remote fallback
        // Comprehensive error handling and monitoring
        // Session correlation and performance tracking
    }
}
```

## Anti-Patterns to Avoid

### 9. Common Mistakes

**❌ Interface Misalignment**:
```kotlin
// WRONG: Different method signatures between interfaces
interface DomainRepository {
    suspend fun getStats(): Result<DomainStats>
}
interface DataRepository {
    suspend fun getStats(): Result<DtoStats> // Different return type!
}
```

**❌ Incomplete Data Interface**:
```kotlin
// WRONG: Data interface missing methods from domain interface
interface DataRepository {
    suspend fun getById(id: String): Result<Entity?>
    // Missing: addEntity, updateEntity, deleteEntity from domain interface
}
```

**❌ Bloated Domain Interface**:
```kotlin
// WRONG: Domain interface with infrastructure operations
interface DomainRepository {
    suspend fun getById(id: String): Result<Entity?>
    suspend fun exportData(format: String): Result<String> // Infrastructure operation!
    suspend fun clearAllCache(): Result<Unit> // Infrastructure operation!
}
```

**✅ Correct Pattern**:
```kotlin
// Domain: Essential business operations only
interface DomainRepository {
    suspend fun getById(id: String): Result<Entity?>
    suspend fun add(entity: Entity): Result<Unit>
    suspend fun update(entity: Entity): Result<Unit>
    suspend fun delete(id: String): Result<Unit>
}

// Data: Comprehensive operations including infrastructure
interface DataRepository {
    // All domain methods (identical signatures)
    suspend fun getById(id: String): Result<Entity?>
    suspend fun add(entity: Entity): Result<Unit>
    suspend fun update(entity: Entity): Result<Unit>
    suspend fun delete(id: String): Result<Unit>
    
    // Plus infrastructure operations
    suspend fun exportData(format: String): Result<String>
    suspend fun clearAllCache(): Result<Unit>
    suspend fun createBackup(): Result<Map<String, Any>>
}
```

## Migration Guide

### 10. Converting Existing Repositories

**Step 1: Analyze Current Interface**
- Identify business vs infrastructure operations
- Check for method signature mismatches
- Document missing methods

**Step 2: Restructure Domain Interface**
- Keep only essential business operations
- Remove infrastructure operations (cache, export, backup)
- Ensure clean, focused contract

**Step 3: Expand Data Interface**
- Include ALL domain interface methods (identical signatures)
- Add comprehensive infrastructure operations
- Ensure complete data management coverage

**Step 4: Update Implementation**
- Implement both interfaces in single class
- Use method delegation where appropriate
- Add comprehensive error handling and monitoring

**Step 5: Update DI Configuration**
- Bind both interfaces to same implementation instance
- Use lazy dependencies to prevent circular references
- Ensure proper dependency injection

**Step 6: Update Usage**
- ViewModels/UseCases use domain interface
- Infrastructure components use data interface
- Verify no "never used" IDE flags

This pattern, demonstrated perfectly by UserProfileRepositoryImpl, eliminates architectural misalignment and creates clean, maintainable repository implementations that serve both business logic and infrastructure needs effectively.
