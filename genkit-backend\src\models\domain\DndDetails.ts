/**
 * TypeScript equivalent of Android domain.model.DndDetails
 * SSoT model for DND (Do Not Deliver) preferences.
 */

export interface CustomDndRule {
  isEnabled: boolean;
  tipAmountThreshold: number;
  comparisonType: ComparisonType;
}

export enum ComparisonType {
  LESS_THAN = "LESS_THAN",
  LESS_THAN_OR_EQUAL_TO = "LESS_THAN_OR_EQUAL_TO", 
  EQUAL_TO = "EQUAL_TO"
}

export interface DndDetails {
  customRule: CustomDndRule | null;
}

export interface AddressDndEvaluation {
  shouldBeDnd: boolean;
  reason: string | null;
  isVerified: boolean;
  confidence: number;
  evaluationDetails: string;
}

export interface DeliveryDndSignal {
  shouldBeDnd: boolean;
  reason: string | null;
  isVerified: boolean;
}