// Auto-generated from Address.kt
// TypeScript equivalent of Android domain.model.Address

/**
 * Domain model generated from Kotlin Address
 * Auto-generated TypeScript equivalent of Android domain model
 */
export interface Address {
  id: string;
  userId?: string | null;
  fullAddress?: string | null;
  normalizedAddress?: string | null;
  placeId?: string | null;
  notes?: string | null;
  tags?: string[] | null;
  components?: AddressComponents | null;
  coordinates?: Coordinates | null;
  isDefault?: boolean | null;
  orderIds?: string[] | null;
  searchTerms?: string[] | null;
  searchFields?: SearchFields | null;
  deliveryStats?: Delivery_stats | null;
  flags?: Flags | null;
  metadata?: Metadata | null;
  platform?: Platform | null;
}